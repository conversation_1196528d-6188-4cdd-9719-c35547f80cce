{"v": "5.6.5", "fr": 60, "ip": 0, "op": 120, "w": 48, "h": 48, "nm": "05 Unlock", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Shape Layer 1", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20, "s": [0]}, {"i": {"x": [0.2], "y": [1]}, "o": {"x": [0.12], "y": [0]}, "t": 24, "s": [0]}, {"i": {"x": [0.7], "y": [1]}, "o": {"x": [0.3], "y": [0]}, "t": 32, "s": [-18]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 38, "s": [-22]}, {"i": {"x": [0.8], "y": [1]}, "o": {"x": [0.8], "y": [0]}, "t": 44, "s": [-22]}, {"i": {"x": [0.571], "y": [1]}, "o": {"x": [0.178], "y": [0]}, "t": 56, "s": [0]}, {"i": {"x": [0.809], "y": [1]}, "o": {"x": [0.382], "y": [0]}, "t": 64, "s": [0]}, {"i": {"x": [0.809], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 76, "s": [0]}, {"t": 84, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.2, "y": 1}, "o": {"x": 0.12, "y": 0}, "t": 0, "s": [5, 2.031, 0], "to": [0, 0.195, 0], "ti": [0, -0.195, 0]}, {"i": {"x": 0.2, "y": 0.2}, "o": {"x": 0.333, "y": 0.333}, "t": 20, "s": [5, 3.2, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.574, "y": 1}, "o": {"x": 0.176, "y": 0}, "t": 24, "s": [5, 3.2, 0], "to": [0, 0.102, 0], "ti": [0, -0.031, 0]}, {"i": {"x": 0.2, "y": 0.2}, "o": {"x": 0.333, "y": 0.333}, "t": 32, "s": [5, 4, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.8, "y": 1}, "o": {"x": 0.8, "y": 0}, "t": 44, "s": [5, 4, 0], "to": [0, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.753, "y": 0}, "o": {"x": 0.3, "y": 0}, "t": 56, "s": [5, 5, 0], "to": [0, 0, 0], "ti": [0, 0.171, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.471, "y": 0.157}, "t": 66, "s": [5, 5, 0], "to": [0, -0.256, 0], "ti": [0, 0.1, 0]}, {"i": {"x": 0.6, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 76, "s": [5, 3.2, 0], "to": [0, -0.167, 0], "ti": [0, 0.1, 0]}, {"t": 84, "s": [5, 4, 0]}], "ix": 2}, "a": {"a": 0, "k": [-5.828, -3.969, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.2, "y": 1}, "o": {"x": 0.12, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [-6.031, 0], [-0.008, 0], [0, -1.751], [0, 0]], "o": [[0, 0], [0, 0], [0.141, 0], [5.219, 0], [0, 0.313], [0, 0]], "v": [[-5.828, -3.969], [-5.828, -9.719], [0.078, -15.203], [0.547, -15.203], [7.078, -9.781], [7.078, -8.563]], "c": false}]}, {"i": {"x": 0.7, "y": 1}, "o": {"x": 0.3, "y": 0}, "t": 20, "s": [{"i": [[0, 0], [0, 0], [-6.031, 0], [-0.008, 0], [0, -1.751], [0, 0]], "o": [[0, 0], [0, 0], [0.141, 0], [5.219, 0], [0, 0.313], [0, 0]], "v": [[-6.828, -3.956], [-6.828, -9.706], [-0.921, -14.995], [1.641, -15.011], [8.172, -9.784], [8.172, -8.566]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.12, "y": 0}, "t": 24, "s": [{"i": [[0, 0], [0, 0], [-6.031, 0], [-0.008, 0], [0, -1.751], [0, 0]], "o": [[0, 0], [0, 0], [0.141, 0], [5.219, 0], [0, 0.313], [0, 0]], "v": [[-6.828, -3.956], [-6.828, -9.706], [-0.921, -14.995], [1.641, -15.011], [8.172, -9.784], [8.172, -8.566]], "c": false}]}, {"i": {"x": 0.2, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [{"i": [[0, 0], [0, 0], [-6.031, 0], [-0.008, 0], [0, -1.751], [0, 0]], "o": [[0, 0], [0, 0], [0.141, 0], [5.219, 0], [0, 0.313], [0, 0]], "v": [[-5.732, -4.929], [-5.829, -10.089], [-0.196, -15.047], [1.301, -15.055], [7.615, -9.783], [7.615, -8.564]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.3, "y": 0}, "t": 32, "s": [{"i": [[0, 0], [0, 0], [-6.031, 0], [-0.008, 0], [0, -1.751], [0, 0]], "o": [[0, 0], [0, 0], [0.141, 0], [5.219, 0], [0, 0.313], [0, 0]], "v": [[-4.728, -5.534], [-4.865, -10.457], [0.504, -15.098], [0.973, -15.098], [7.078, -9.781], [7.078, -8.563]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [{"i": [[0, 0], [0, 0], [-6.031, 0], [-0.008, 0], [0, -1.751], [0, 0]], "o": [[0, 0], [0, 0], [0.141, 0], [5.219, 0], [0, 0.313], [0, 0]], "v": [[-4.623, -5.844], [-4.865, -10.457], [0.504, -15.098], [0.973, -15.098], [7.078, -9.781], [7.078, -8.563]], "c": false}]}, {"i": {"x": 0.7, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[0, 0], [0, 0], [-6.031, 0], [-0.008, 0], [0, -1.751], [0, 0]], "o": [[0, 0], [0, 0], [0.141, 0], [5.219, 0], [0, 0.313], [0, 0]], "v": [[-4.616, -5.887], [-4.865, -10.457], [0.504, -15.098], [0.973, -15.098], [7.078, -9.781], [7.078, -8.563]], "c": false}]}, {"i": {"x": 0.8, "y": 1}, "o": {"x": 0.8, "y": 0}, "t": 44, "s": [{"i": [[0, 0], [0, 0], [-6.031, 0], [-0.008, 0], [0, -1.751], [0, 0]], "o": [[0, 0], [0, 0], [0.141, 0], [5.219, 0], [0, 0.313], [0, 0]], "v": [[-4.636, -5.885], [-4.865, -10.457], [0.504, -15.098], [0.973, -15.098], [7.078, -9.781], [7.078, -8.563]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.3, "y": 0}, "t": 56, "s": [{"i": [[0, 0], [0, 0], [-6.031, 0], [-0.008, 0], [0, -1.751], [0, 0]], "o": [[0, 0], [0, 0], [0.141, 0], [5.219, 0], [0, 0.313], [0, 0]], "v": [[-4.811, -5.936], [-4.888, -9.77], [0.469, -15.191], [0.938, -15.191], [6.149, -9.094], [6.165, -6.062]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [{"i": [[0, 0], [0, 0], [-6.031, 0], [-0.008, 0], [0, -1.751], [0, 0]], "o": [[0, 0], [0, 0], [0.141, 0], [5.219, 0], [0, 0.313], [0, 0]], "v": [[-5.942, -6.445], [-6.001, -9.233], [-0.644, -14.655], [1.825, -14.679], [7.036, -8.581], [7.053, -6.547]], "c": false}]}, {"i": {"x": 0.7, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [{"i": [[0, 0], [0, 0], [-6.031, 0], [-0.008, 0], [0, -1.751], [0, 0]], "o": [[0, 0], [0, 0], [0.141, 0], [5.219, 0], [0, 0.313], [0, 0]], "v": [[-5.263, -6.36], [-5.29, -8.732], [0.058, -14.542], [1.284, -14.551], [6.504, -8.065], [6.508, -6.399]], "c": false}]}, {"i": {"x": 0.7, "y": 1}, "o": {"x": 0.3, "y": 0}, "t": 76, "s": [{"i": [[0, 0], [0, 0], [-6.031, 0], [-0.008, 0], [0, -1.751], [0, 0]], "o": [[0, 0], [0, 0], [0.141, 0], [5.219, 0], [0, 0.313], [0, 0]], "v": [[-4.858, -6.279], [-4.857, -8.426], [0.485, -14.473], [0.954, -14.473], [6.18, -7.75], [6.165, -6.281]], "c": false}]}, {"t": 84, "s": [{"i": [[0, 0], [0, 0], [-6.031, 0], [-0.008, 0], [0, -1.751], [0, 0]], "o": [[0, 0], [0, 0], [0.141, 0], [5.219, 0], [0, 0.313], [0, 0]], "v": [[-4.858, -6.108], [-4.857, -8.426], [0.5, -13.848], [0.969, -13.848], [6.18, -7.75], [6.165, -6.109]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Lock 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.2, "y": 1}, "o": {"x": 0.12, "y": 0}, "t": 0, "s": [24.5, 28.29, 0], "to": [0, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.2, "y": 0.2}, "o": {"x": 0.12, "y": 0.12}, "t": 20, "s": [24.5, 30.29, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.2, "y": 1}, "o": {"x": 0.12, "y": 0}, "t": 24, "s": [24.5, 30.29, 0], "to": [0, -0.882, 0], "ti": [-0.031, -0.063, 0]}, {"i": {"x": 0.7, "y": 1}, "o": {"x": 0.3, "y": 0}, "t": 32, "s": [24.5, 25, 0], "to": [0.224, 0.447, 0], "ti": [0, 0.167, 0]}, {"i": {"x": 0.8, "y": 1}, "o": {"x": 0.8, "y": 0}, "t": 44, "s": [24.5, 24, 0], "to": [0, -0.167, 0], "ti": [0, -0.715, 0]}, {"i": {"x": 0.833, "y": 0.665}, "o": {"x": 0.3, "y": 0}, "t": 56, "s": [24.5, 30.29, 0], "to": [0, 0.346, 0], "ti": [0, 0.52, 0]}, {"i": {"x": 0.7, "y": 1}, "o": {"x": 0.167, "y": 0.184}, "t": 64, "s": [24.5, 29.737, 0], "to": [0, -0.556, 0], "ti": [0, 0.172, 0]}, {"i": {"x": 0.7, "y": 1}, "o": {"x": 0.3, "y": 0}, "t": 76, "s": [24.5, 27.5, 0], "to": [0, 0, 0], "ti": [0, 0.172, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.12, "y": 0.12}, "t": 84, "s": [24.5, 28.29, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 100, "s": [24.5, 28.29, 0]}], "ix": 2}, "a": {"a": 0, "k": [11.5, 10.29, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.2, "y": 1}, "o": {"x": 0.12, "y": 0}, "t": 0, "s": [{"i": [[-1.657, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0]], "o": [[0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0], [-1.657, 0], [0, 0], [0, -1.657]], "v": [[-6.5, -8.29], [6.5, -8.29], [9.5, -5.29], [9.5, 5.29], [6.5, 8.29], [-6.5, 8.29], [-9.5, 5.29], [-9.5, -5.29]], "c": true}]}, {"i": {"x": 0.7, "y": 1}, "o": {"x": 0.3, "y": 0}, "t": 20, "s": [{"i": [[-1.657, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0]], "o": [[0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0], [-1.657, 0], [0, 0], [0, -1.657]], "v": [[-7.938, -6.937], [8, -6.937], [11, -3.937], [11, 5.298], [8, 8.298], [-7.938, 8.298], [-10.938, 5.298], [-10.938, -3.937]], "c": true}]}, {"i": {"x": 0.2, "y": 1}, "o": {"x": 0.12, "y": 0}, "t": 24, "s": [{"i": [[-1.657, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0]], "o": [[0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0], [-1.657, 0], [0, 0], [0, -1.657]], "v": [[-7.938, -6.937], [8, -6.937], [11, -3.937], [11, 5.298], [8, 8.298], [-7.938, 8.298], [-10.938, 5.298], [-10.938, -3.937]], "c": true}]}, {"i": {"x": 0.7, "y": 1}, "o": {"x": 0.3, "y": 0}, "t": 32, "s": [{"i": [[-1.657, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0]], "o": [[0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0], [-1.657, 0], [0, 0], [0, -1.657]], "v": [[-5.469, -8.29], [5.469, -8.29], [8.469, -5.29], [8.469, 6.071], [5.469, 9.071], [-5.469, 9.071], [-8.469, 6.071], [-8.469, -5.29]], "c": true}]}, {"i": {"x": 0.8, "y": 1}, "o": {"x": 0.8, "y": 0}, "t": 44, "s": [{"i": [[-1.657, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0]], "o": [[0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0], [-1.657, 0], [0, 0], [0, -1.657]], "v": [[-6.5, -8.29], [6.5, -8.29], [9.5, -5.29], [9.5, 5.29], [6.5, 8.29], [-6.5, 8.29], [-9.5, 5.29], [-9.5, -5.29]], "c": true}]}, {"i": {"x": 0.7, "y": 1}, "o": {"x": 0.3, "y": 0}, "t": 56, "s": [{"i": [[-1.657, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0]], "o": [[0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0], [-1.657, 0], [0, 0], [0, -1.657]], "v": [[-6.469, -6.952], [6.5, -6.952], [9.5, -3.952], [9.5, 5.283], [6.5, 8.283], [-6.469, 8.283], [-9.469, 5.283], [-9.469, -3.952]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.3, "y": 0}, "t": 64, "s": [{"i": [[-1.657, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0]], "o": [[0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0], [-1.657, 0], [0, 0], [0, -1.657]], "v": [[-7.938, -6.937], [8, -6.937], [11, -3.937], [11, 5.298], [8, 8.298], [-7.938, 8.298], [-10.938, 5.298], [-10.938, -3.937]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.3, "y": 0}, "t": 76, "s": [{"i": [[-1.657, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0]], "o": [[0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0], [-1.657, 0], [0, 0], [0, -1.657]], "v": [[-5.813, -8.727], [5.813, -8.712], [8.813, -5.712], [8.813, 5.304], [5.813, 8.304], [-5.813, 8.29], [-8.813, 5.29], [-8.813, -5.727]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.12, "y": 0}, "t": 84, "s": [{"i": [[-1.657, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0]], "o": [[0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0], [-1.657, 0], [0, 0], [0, -1.657]], "v": [[-6.5, -8.29], [6.5, -8.29], [9.5, -5.29], [9.5, 5.29], [6.5, 8.29], [-6.5, 8.29], [-9.5, 5.29], [-9.5, -5.29]], "c": true}]}, {"t": 100, "s": [{"i": [[-1.657, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0]], "o": [[0, 0], [1.657, 0], [0, 0], [0, 1.656], [0, 0], [-1.657, 0], [0, 0], [0, -1.657]], "v": [[-6.5, -8.29], [6.5, -8.29], [9.5, -5.29], [9.5, 5.29], [6.5, 8.29], [-6.5, 8.29], [-9.5, 5.29], [-9.5, -5.29]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [11.5, 10.29], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Middle circle 2", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.2, "y": 1}, "o": {"x": 0.12, "y": 0}, "t": 0, "s": [11.5, 10.5, 0], "to": [0, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.2, "y": 0.2}, "o": {"x": 0.333, "y": 0.333}, "t": 20, "s": [11.5, 11.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.2, "y": 1}, "o": {"x": 0.12, "y": 0}, "t": 24, "s": [11.5, 11.5, 0], "to": [0, -0.417, 0], "ti": [0, 0.417, 0]}, {"i": {"x": 0.2, "y": 0.2}, "o": {"x": 0.12, "y": 0.12}, "t": 32, "s": [11.5, 9, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.8, "y": 1}, "o": {"x": 0.8, "y": 0}, "t": 44, "s": [11.5, 9, 0], "to": [0, 0.417, 0], "ti": [0, -0.15, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 56, "s": [11.5, 11.5, 0], "to": [0, 0.15, 0], "ti": [0, 0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.3, "y": 0}, "t": 76, "s": [11.5, 9.9, 0], "to": [0, -0.167, 0], "ti": [0, -0.1, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.12, "y": 0.12}, "t": 84, "s": [11.5, 10.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 100, "s": [11.5, 10.5, 0]}], "ix": 2}, "a": {"a": 0, "k": [4.5, 4.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.2, "y": 1}, "o": {"x": 0.12, "y": 0}, "t": 0, "s": [{"i": [[-1.381, 0], [0, -1.381], [1.381, 0], [0, 1.381]], "o": [[1.381, 0], [0, 1.381], [-1.381, 0], [0, -1.381]], "v": [[0, -2.5], [2.5, 0], [0, 2.5], [-2.5, 0]], "c": true}]}, {"i": {"x": 0.7, "y": 1}, "o": {"x": 0.3, "y": 0}, "t": 20, "s": [{"i": [[-1.381, 0], [0, -1.381], [1.381, 0], [0, 1.381]], "o": [[1.381, 0], [0, 1.381], [-1.381, 0], [0, -1.381]], "v": [[0, -2.5], [3.5, 0], [0, 2.5], [-3.516, 0]], "c": true}]}, {"i": {"x": 0.2, "y": 1}, "o": {"x": 0.12, "y": 0}, "t": 24, "s": [{"i": [[-1.381, 0], [0, -1.381], [1.381, 0], [0, 1.381]], "o": [[1.381, 0], [0, 1.381], [-1.381, 0], [0, -1.381]], "v": [[0, -2.5], [3.5, 0], [0, 2.5], [-3.516, 0]], "c": true}]}, {"i": {"x": 0.7, "y": 1}, "o": {"x": 0.3, "y": 0}, "t": 32, "s": [{"i": [[-1.381, 0], [0, -1.381], [1.381, 0], [0, 1.381]], "o": [[1.381, 0], [0, 1.381], [-1.381, 0], [0, -1.381]], "v": [[0, -2.5], [2.5, 0], [0, 3.766], [-2.5, 0]], "c": true}]}, {"i": {"x": 0.8, "y": 1}, "o": {"x": 0.8, "y": 0}, "t": 44, "s": [{"i": [[-1.381, 0], [0, -1.381], [1.381, 0], [0, 1.381]], "o": [[1.381, 0], [0, 1.381], [-1.381, 0], [0, -1.381]], "v": [[0, -2.5], [2.5, 0], [0, 2.5], [-2.5, 0]], "c": true}]}, {"i": {"x": 0.8, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 52, "s": [{"i": [[-1.381, 0], [0, -1.381], [1.381, 0], [0, 1.381]], "o": [[1.381, 0], [0, 1.381], [-1.381, 0], [0, -1.381]], "v": [[0, -3.594], [2.5, 0], [0, 2.5], [-2.5, 0]], "c": true}]}, {"i": {"x": 0.7, "y": 1}, "o": {"x": 0.3, "y": 0}, "t": 56, "s": [{"i": [[-1.381, 0], [0, -1.381], [1.381, 0], [0, 1.381]], "o": [[1.381, 0], [0, 1.381], [-1.381, 0], [0, -1.381]], "v": [[0, -3.594], [2.5, 0], [0, 2.5], [-2.5, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 64, "s": [{"i": [[-1.381, 0], [0, -1.381], [1.381, 0], [0, 1.381]], "o": [[1.381, 0], [0, 1.381], [-1.381, 0], [0, -1.381]], "v": [[0, -2.5], [3.5, 0], [0, 2.5], [-3.516, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.3, "y": 0}, "t": 76, "s": [{"i": [[-1.381, 0], [0, -1.381], [1.381, 0], [0, 1.381]], "o": [[1.381, 0], [0, 1.381], [-1.381, 0], [0, -1.381]], "v": [[0, -2.5], [2.5, 0], [0, 2.781], [-2.5, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.12, "y": 0}, "t": 84, "s": [{"i": [[-1.381, 0], [0, -1.381], [1.381, 0], [0, 1.381]], "o": [[1.381, 0], [0, 1.381], [-1.381, 0], [0, -1.381]], "v": [[0, -2.5], [2.5, 0], [0, 2.5], [-2.5, 0]], "c": true}]}, {"t": 100, "s": [{"i": [[-1.381, 0], [0, -1.381], [1.381, 0], [0, 1.381]], "o": [[1.381, 0], [0, 1.381], [-1.381, 0], [0, -1.381]], "v": [[0, -2.5], [2.5, 0], [0, 2.5], [-2.5, 0]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [4.5, 4.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Circle", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [24, 24, 0], "ix": 2}, "a": {"a": 0, "k": [25, 25, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-12.703, 0], [0, -12.703], [12.702, 0], [0, 12.702]], "o": [[12.702, 0], [0, 12.702], [-12.703, 0], [0, -12.703]], "v": [[0, -23], [23, 0], [0, 23], [-23, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [25, 25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}], "markers": []}