// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include <map>

// DO NOT EDIT -- DO NOT EDIT -- DO NOT EDIT
// This file is generated by
// flutter/flutter:dev/tools/gen_keycodes/bin/gen_keycodes.dart and should not
// be edited directly.
//
// Edit the template
// flutter/flutter:dev/tools/gen_keycodes/data/fuchsia_keyboard_map_cc.tmpl
// instead.
//
// See flutter/flutter:dev/tools/gen_keycodes/README.md for more information.

/// Maps Fuchsia-specific IDs to the matching LogicalKeyboardKey.
const std::map<int, int> g_fuchsia_to_logical_key = {
@@@FUCHSIA_KEY_CODE_MAP@@@
};

/// Maps Fuchsia-specific USB HID Usage IDs to the matching
/// [PhysicalKeyboardKey].
const std::map<int, int> g_fuchsia_to_physical_key = {
@@@FUCHSIA_SCAN_CODE_MAP@@@
};
