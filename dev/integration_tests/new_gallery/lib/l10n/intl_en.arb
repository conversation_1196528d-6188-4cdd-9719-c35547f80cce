{"githubRepo": "{repoName} GitHub repository", "@githubRepo": {"description": "Represents a link to a GitHub repository.", "placeholders": {"repoName": {"example": "Flutter Gallery"}}}, "aboutDialogDescription": "To see the source code for this app, please visit the {repoLink}.", "@aboutDialogDescription": {"description": "A description about how to view the source code for this app.", "placeholders": {"repoLink": {"example": "Flutter Gallery GitHub repository"}}}, "deselect": "Deselect", "@deselect": {"description": "Deselect a (selectable) item"}, "notSelected": "Not selected", "@notSelected": {"description": "Indicates the status of a (selectable) item not being selected"}, "select": "Select", "@select": {"description": "Select a (selectable) item"}, "selectable": "Selectable (long press)", "@selectable": {"description": "Indicates the associated piece of UI is selectable by long pressing it"}, "selected": "Selected", "@selected": {"description": "Indicates status of a (selectable) item being selected"}, "signIn": "SIGN IN", "@signIn": {"description": "Sign in label to sign into website."}, "bannerDemoText": "Your password was updated on your other device. Please sign in again.", "@bannerDemoText": {"description": "Password was updated on a different device and the user is required to sign in again"}, "bannerDemoResetText": "Reset the banner", "@bannerDemoResetText": {"description": "Show the Banner to the user again."}, "bannerDemoMultipleText": "Multiple actions", "@bannerDemoMultipleText": {"description": "When the user clicks this button the Banner will toggle multiple actions or a single action"}, "bannerDemoLeadingText": "Leading Icon", "@bannerDemoLeadingText": {"description": "If user clicks this button the leading icon in the Banner will disappear"}, "dismiss": "DISMISS", "@dismiss": {"description": "When text is pressed the banner widget will be removed from the screen."}, "backToGallery": "Back to Gallery", "@backToGallery": {"description": "Semantic label for back button to exit a study and return to the gallery home page."}, "cardsDemoExplore": "Explore", "@cardsDemoExplore": {"description": "Click to see more about the content in the cards demo."}, "cardsDemoExploreSemantics": "Explore {destinationName}", "@cardsDemoExploreSemantics": {"description": "Semantics label for Explore. Label tells user to explore the destinationName to the user. Example Explore Tamil", "placeholders": {"destinationName": {"example": "Tamil"}}}, "cardsDemoShareSemantics": "Share {destinationName}", "@cardsDemoShareSemantics": {"description": "Semantics label for Share. Label tells user to share the destinationName to the user. Example Share Tamil", "placeholders": {"destinationName": {"example": "Tamil"}}}, "cardsDemoTappable": "Tappable", "@cardsDemoTappable": {"description": "The user can tap this button"}, "cardsDemoTravelDestinationTitle1": "Top 10 Cities to Visit in Tamil Nadu", "@cardsDemoTravelDestinationTitle1": {"description": "The top 10 cities that you can visit in Tamil Nadu"}, "cardsDemoTravelDestinationDescription1": "Number 10", "@cardsDemoTravelDestinationDescription1": {"description": "Number 10"}, "cardsDemoTravelDestinationCity1": "Thanjavur", "@cardsDemoTravelDestinationCity1": {"description": "Thanjavur the city"}, "cardsDemoTravelDestinationLocation1": "Thanjavur, Tamil Nadu", "@cardsDemoTravelDestinationLocation1": {"description": "Thanjavur, Tamil Nadu is a location"}, "cardsDemoTravelDestinationTitle2": "Artisans of Southern India", "@cardsDemoTravelDestinationTitle2": {"description": "Artist that are from Southern India"}, "cardsDemoTravelDestinationDescription2": "Silk Spinners", "@cardsDemoTravelDestinationDescription2": {"description": "Silk Spinners"}, "cardsDemoTravelDestinationCity2": "Chettinad", "@cardsDemoTravelDestinationCity2": {"description": "Chettinad the city"}, "cardsDemoTravelDestinationLocation2": "Sivaganga, Tamil Nadu", "@cardsDemoTravelDestinationLocation2": {"description": "Sivaganga, Tamil Nadu is a location"}, "cardsDemoTravelDestinationTitle3": "Brihadisvara Temple", "@cardsDemoTravelDestinationTitle3": {"description": "Brihadisvara Temple"}, "cardsDemoTravelDestinationDescription3": "Temples", "@cardsDemoTravelDestinationDescription3": {"description": "Temples"}, "homeHeaderGallery": "Gallery", "@homeHeaderGallery": {"description": "Header title on home screen for Gallery section."}, "homeHeaderCategories": "Categories", "@homeHeaderCategories": {"description": "Header title on home screen for Categories section."}, "shrineDescription": "A fashionable retail app", "@shrineDescription": {"description": "Study description for Shrine."}, "fortnightlyDescription": "A content-focused news app", "@fortnightlyDescription": {"description": "Study description for Fortnightly."}, "rallyDescription": "A personal finance app", "@rallyDescription": {"description": "Study description for Rally."}, "replyDescription": "An efficient, focused email app", "@replyDescription": {"description": "Study description for Reply."}, "rallyAccountDataChecking": "Checking", "@rallyAccountDataChecking": {"description": "Name for account made up by user."}, "rallyAccountDataHomeSavings": "Home Savings", "@rallyAccountDataHomeSavings": {"description": "Name for account made up by user."}, "rallyAccountDataCarSavings": "Car Savings", "@rallyAccountDataCarSavings": {"description": "Name for account made up by user."}, "rallyAccountDataVacation": "Vacation", "@rallyAccountDataVacation": {"description": "Name for account made up by user."}, "rallyAccountDetailDataAnnualPercentageYield": "Annual Percentage Yield", "@rallyAccountDetailDataAnnualPercentageYield": {"description": "Title for account statistics. Below a percentage such as 0.10% will be displayed."}, "rallyAccountDetailDataInterestRate": "Interest Rate", "@rallyAccountDetailDataInterestRate": {"description": "Title for account statistics. Below a dollar amount such as $100 will be displayed."}, "rallyAccountDetailDataInterestYtd": "Interest YTD", "@rallyAccountDetailDataInterestYtd": {"description": "Title for account statistics. Below a dollar amount such as $100 will be displayed."}, "rallyAccountDetailDataInterestPaidLastYear": "Interest Paid Last Year", "@rallyAccountDetailDataInterestPaidLastYear": {"description": "Title for account statistics. Below a dollar amount such as $100 will be displayed."}, "rallyAccountDetailDataNextStatement": "Next Statement", "@rallyAccountDetailDataNextStatement": {"description": "Title for an account detail. Below a date for when the next account statement is released."}, "rallyAccountDetailDataAccountOwner": "Account Owner", "@rallyAccountDetailDataAccountOwner": {"description": "Title for an account detail. Below the name of the account owner will be displayed."}, "rallyBillDetailTotalAmount": "Total Amount", "@rallyBillDetailTotalAmount": {"description": "Title for column where it displays the total dollar amount that the user has in bills."}, "rallyBillDetailAmountPaid": "Amount <PERSON>", "@rallyBillDetailAmountPaid": {"description": "Title for column where it displays the amount that the user has paid."}, "rallyBillDetailAmountDue": "Amount Due", "@rallyBillDetailAmountDue": {"description": "Title for column where it displays the amount that the user has due."}, "rallyBudgetCategoryCoffeeShops": "Coffee Shops", "@rallyBudgetCategoryCoffeeShops": {"description": "Category for budget, to sort expenses / bills in."}, "rallyBudgetCategoryGroceries": "Groceries", "@rallyBudgetCategoryGroceries": {"description": "Category for budget, to sort expenses / bills in."}, "rallyBudgetCategoryRestaurants": "Restaurants", "@rallyBudgetCategoryRestaurants": {"description": "Category for budget, to sort expenses / bills in."}, "rallyBudgetCategoryClothing": "Clothing", "@rallyBudgetCategoryClothing": {"description": "Category for budget, to sort expenses / bills in."}, "rallyBudgetDetailTotalCap": "Total Cap", "@rallyBudgetDetailTotalCap": {"description": "Title for column where it displays the total dollar cap that the user has for its budget."}, "rallyBudgetDetailAmountUsed": "Amount Used", "@rallyBudgetDetailAmountUsed": {"description": "Title for column where it displays the dollar amount that the user has used in its budget."}, "rallyBudgetDetailAmountLeft": "Amount Left", "@rallyBudgetDetailAmountLeft": {"description": "Title for column where it displays the dollar amount that the user has left in its budget."}, "rallySettingsManageAccounts": "Manage Accounts", "@rallySettingsManageAccounts": {"description": "Link to go to the page 'Manage Accounts."}, "rallySettingsTaxDocuments": "Tax Documents", "@rallySettingsTaxDocuments": {"description": "Link to go to the page 'Tax Documents'."}, "rallySettingsPasscodeAndTouchId": "Passcode and Touch ID", "@rallySettingsPasscodeAndTouchId": {"description": "Link to go to the page 'Passcode and Touch ID'."}, "rallySettingsNotifications": "Notifications", "@rallySettingsNotifications": {"description": "Link to go to the page 'Notifications'."}, "rallySettingsPersonalInformation": "Personal Information", "@rallySettingsPersonalInformation": {"description": "Link to go to the page 'Personal Information'."}, "rallySettingsPaperlessSettings": "Paperless Settings", "@rallySettingsPaperlessSettings": {"description": "Link to go to the page 'Paperless Settings'."}, "rallySettingsFindAtms": "Find ATMs", "@rallySettingsFindAtms": {"description": "Link to go to the page 'Find ATMs'."}, "rallySettingsHelp": "Help", "@rallySettingsHelp": {"description": "Link to go to the page 'Help'."}, "rallySettingsSignOut": "Sign out", "@rallySettingsSignOut": {"description": "Link to go to the page 'Sign out'."}, "rallyAccountTotal": "Total", "@rallyAccountTotal": {"description": "Title for 'total account value' overview page, a dollar value is displayed next to it."}, "rallyBillsDue": "Due", "@rallyBillsDue": {"description": "Title for 'bills due' page, a dollar value is displayed next to it."}, "rallyBudgetLeft": "Left", "@rallyBudgetLeft": {"description": "Title for 'budget left' page, a dollar value is displayed next to it."}, "rallyAccounts": "Accounts", "@rallyAccounts": {"description": "Link text for accounts page."}, "rallyBills": "Bills", "@rallyBills": {"description": "Link text for bills page."}, "rallyBudgets": "Budgets", "@rallyBudgets": {"description": "Link text for budgets page."}, "rallyAlerts": "<PERSON><PERSON><PERSON>", "@rallyAlerts": {"description": "Title for alerts part of overview page."}, "rallySeeAll": "SEE ALL", "@rallySeeAll": {"description": "Link text for button to see all data for category."}, "rallyFinanceLeft": " LEFT", "@rallyFinanceLeft": {"description": "Displayed as 'dollar amount left', for example $46.70 LEFT, for a budget category."}, "rallyTitleOverview": "OVERVIEW", "@rallyTitleOverview": {"description": "The navigation link to the overview page."}, "rallyTitleAccounts": "ACCOUNTS", "@rallyTitleAccounts": {"description": "The navigation link to the accounts page."}, "rallyTitleBills": "BILLS", "@rallyTitleBills": {"description": "The navigation link to the bills page."}, "rallyTitleBudgets": "BUDGETS", "@rallyTitleBudgets": {"description": "The navigation link to the budgets page."}, "rallyTitleSettings": "SETTINGS", "@rallyTitleSettings": {"description": "The navigation link to the settings page."}, "rallyLoginLoginToRally": "<PERSON>gin to Rally", "@rallyLoginLoginToRally": {"description": "Title for login page for the Rally app (Rally does not need to be translated as it is a product name)."}, "rallyLoginNoAccount": "Don't have an account?", "@rallyLoginNoAccount": {"description": "Prompt for signing up for an account."}, "rallyLoginSignUp": "SIGN UP", "@rallyLoginSignUp": {"description": "Button text to sign up for an account."}, "rallyLoginUsername": "Username", "@rallyLoginUsername": {"description": "The username field in an login form."}, "rallyLoginPassword": "Password", "@rallyLoginPassword": {"description": "The password field in an login form."}, "rallyLoginLabelLogin": "<PERSON><PERSON>", "@rallyLoginLabelLogin": {"description": "The label text to login."}, "rallyLoginRememberMe": "Remember Me", "@rallyLoginRememberMe": {"description": "Text if the user wants to stay logged in."}, "rallyLoginButtonLogin": "LOGIN", "@rallyLoginButtonLogin": {"description": "Text for login button."}, "rallyAlertsMessageHeadsUpShopping": "Heads up, you've used up {percent} of your Shopping budget for this month.", "@rallyAlertsMessageHeadsUpShopping": {"description": "Alert message shown when for example, user has used more than 90% of their shopping budget.", "placeholders": {"percent": {"example": "90%"}}}, "rallyAlertsMessageSpentOnRestaurants": "You've spent {amount} on Restaurants this week.", "@rallyAlertsMessageSpentOnRestaurants": {"description": "Alert message shown when for example, user has spent $120 on Restaurants this week.", "placeholders": {"amount": {"example": "$120"}}}, "rallyAlertsMessageATMFees": "You've spent {amount} in ATM fees this month", "@rallyAlertsMessageATMFees": {"description": "Alert message shown when for example, the user has spent $24 in ATM fees this month.", "placeholders": {"amount": {"example": "24"}}}, "rallyAlertsMessageCheckingAccount": "Good work! Your checking account is {percent} higher than last month.", "@rallyAlertsMessageCheckingAccount": {"description": "Alert message shown when for example, the checking account is 1% higher than last month.", "placeholders": {"percent": {"example": "1%"}}}, "rallyAlertsMessageUnassignedTransactions": "{count, plural, =1{Increase your potential tax deduction! Assign categories to 1 unassigned transaction.}other{Increase your potential tax deduction! Assign categories to {count} unassigned transactions.}}", "@rallyAlertsMessageUnassignedTransactions": {"description": "Alert message shown when you have unassigned transactions.", "placeholders": {"count": {"example": "2"}}}, "rallySeeAllAccounts": "See all accounts", "@rallySeeAllAccounts": {"description": "Semantics label for button to see all accounts. Accounts refer to bank account here."}, "rallySeeAllBills": "See all bills", "@rallySeeAllBills": {"description": "Semantics label for button to see all bills."}, "rallySeeAllBudgets": "See all budgets", "@rallySeeAllBudgets": {"description": "Semantics label for button to see all budgets."}, "rallyAccountAmount": "{accountName} account {accountNumber} with {amount}.", "@rallyAccountAmount": {"description": "Semantics label for row with bank account name (for example checking) and its bank account number (for example 123), with how much money is deposited in it (for example $12).", "placeholders": {"accountName": {"example": "Home Savings"}, "accountNumber": {"example": "1234"}, "amount": {"example": "$12"}}}, "rallyBillAmount": "{billName} bill due {date} for {amount}.", "@rallyBillAmount": {"description": "Semantics label for row with a bill (example name is rent), when the bill is due (1/12/2019 for example) and for how much money ($12).", "placeholders": {"billName": {"example": "Rent"}, "date": {"example": "1/24/2019"}, "amount": {"example": "$12"}}}, "rallyBudgetAmount": "{budgetName} budget with {amountUsed} used of {amountTotal}, {amountLeft} left", "@rallyBudgetAmount": {"description": "Semantics label for row with a budget (housing budget for example), with how much is used of the budget (for example $5), the total budget (for example $100) and the amount left in the budget (for example $95).", "placeholders": {"budgetName": {"example": "Groceries"}, "amountUsed": {"example": "$5"}, "amountTotal": {"example": "$100"}, "amountLeft": {"example": "$95"}}}, "craneDescription": "A personalized travel app", "@craneDescription": {"description": "Study description for <PERSON>."}, "homeCategoryReference": "STYLES & OTHER", "@homeCategoryReference": {"description": "Category title on home screen for styles & other demos (for context, the styles demos consist of a color demo and a typography demo)."}, "demoInvalidURL": "Couldn't display URL:", "@demoInvalidURL": {"description": "Error message when opening the URL for a demo."}, "demoOptionsTooltip": "Options", "@demoOptionsTooltip": {"description": "Tooltip for options button in a demo."}, "demoInfoTooltip": "Info", "@demoInfoTooltip": {"description": "Tooltip for info button in a demo."}, "demoCodeTooltip": "Demo Code", "@demoCodeTooltip": {"description": "Tooltip for demo code button in a demo."}, "demoDocumentationTooltip": "API Documentation", "@demoDocumentationTooltip": {"description": "Tooltip for API documentation button in a demo."}, "demoFullscreenTooltip": "Full Screen", "@demoFullscreenTooltip": {"description": "Tooltip for Full Screen button in a demo."}, "demoCodeViewerCopyAll": "COPY ALL", "@demoCodeViewerCopyAll": {"description": "Caption for a button to copy all text."}, "demoCodeViewerCopiedToClipboardMessage": "Copied to clipboard.", "@demoCodeViewerCopiedToClipboardMessage": {"description": "A message displayed to the user after clicking the COPY ALL button, if the text is successfully copied to the clipboard."}, "demoCodeViewerFailedToCopyToClipboardMessage": "Failed to copy to clipboard: {error}", "@demoCodeViewerFailedToCopyToClipboardMessage": {"description": "A message displayed to the user after clicking the COPY ALL button, if the text CANNOT be copied to the clipboard.", "placeholders": {"error": {"example": "Your browser does not have clipboard support."}}}, "demoOptionsFeatureTitle": "View options", "@demoOptionsFeatureTitle": {"description": "Title for an alert that explains what the options button does."}, "demoOptionsFeatureDescription": "Tap here to view available options for this demo.", "@demoOptionsFeatureDescription": {"description": "Description for an alert that explains what the options button does."}, "settingsTitle": "Settings", "@settingsTitle": {"description": "Title for the settings screen."}, "settingsButtonLabel": "Settings", "@settingsButtonLabel": {"description": "Accessibility label for the settings button when settings are not showing."}, "settingsButtonCloseLabel": "Close settings", "@settingsButtonCloseLabel": {"description": "Accessibility label for the settings button when settings are showing."}, "settingsSystemDefault": "System", "@settingsSystemDefault": {"description": "Option label to indicate the system default will be used."}, "settingsTextScaling": "Text scaling", "@settingsTextScaling": {"description": "Title for text scaling setting."}, "settingsTextScalingSmall": "Small", "@settingsTextScalingSmall": {"description": "Option label for small text scale setting."}, "settingsTextScalingNormal": "Normal", "@settingsTextScalingNormal": {"description": "Option label for normal text scale setting."}, "settingsTextScalingLarge": "Large", "@settingsTextScalingLarge": {"description": "Option label for large text scale setting."}, "settingsTextScalingHuge": "<PERSON>ge", "@settingsTextScalingHuge": {"description": "Option label for huge text scale setting."}, "settingsTextDirection": "Text direction", "@settingsTextDirection": {"description": "Title for text direction setting."}, "settingsTextDirectionLocaleBased": "Based on locale", "@settingsTextDirectionLocaleBased": {"description": "Option label for locale-based text direction setting."}, "settingsTextDirectionLTR": "LTR", "@settingsTextDirectionLTR": {"description": "Option label for left-to-right text direction setting."}, "settingsTextDirectionRTL": "RTL", "@settingsTextDirectionRTL": {"description": "Option label for right-to-left text direction setting."}, "settingsLocale": "Locale", "@settingsLocale": {"description": "Title for locale setting."}, "settingsPlatformMechanics": "Platform mechanics", "@settingsPlatformMechanics": {"description": "Title for platform mechanics (iOS, Android, macOS, etc.) setting."}, "settingsTheme": "Theme", "@settingsTheme": {"description": "Title for the theme setting."}, "settingsDarkTheme": "Dark", "@settingsDarkTheme": {"description": "Title for the dark theme setting."}, "settingsLightTheme": "Light", "@settingsLightTheme": {"description": "Title for the light theme setting."}, "settingsSlowMotion": "Slow motion", "@settingsSlowMotion": {"description": "Title for slow motion setting."}, "settingsAbout": "About Flutter Gallery", "@settingsAbout": {"description": "Title for information button."}, "settingsFeedback": "Send feedback", "@settingsFeedback": {"description": "Title for feedback button."}, "settingsAttribution": "Designed by TOASTER in London", "@settingsAttribution": {"description": "Title for attribution (TOASTER is a proper name and should remain in English)."}, "demoAppBarTitle": "App bar", "@demoAppBarTitle": {"description": "Title for the material App bar component demo."}, "demoAppBarSubtitle": "Displays information and actions relating to the current screen", "@demoAppBarSubtitle": {"description": "Subtitle for the material App bar component demo."}, "demoAppBarDescription": "The App bar provides content and actions related to the current screen. It's used for branding, screen titles, navigation, and actions", "@demoAppBarDescription": {"description": "Description for the material App bar component demo."}, "demoBottomAppBarTitle": "Bottom app bar", "@demoBottomAppBarTitle": {"description": "Title for the material bottom app bar component demo."}, "demoBottomAppBarSubtitle": "Displays navigation and actions at the bottom", "@demoBottomAppBarSubtitle": {"description": "Subtitle for the material bottom app bar component demo."}, "demoBottomAppBarDescription": "Bottom app bars provide access to a bottom navigation drawer and up to four actions, including the floating action button.", "@demoBottomAppBarDescription": {"description": "Description for the material bottom app bar component demo."}, "bottomAppBarNotch": "Notch", "@bottomAppBarNotch": {"description": "A toggle for whether to have a notch (or cutout) in the bottom app bar demo."}, "bottomAppBarPosition": "Floating Action Button Position", "@bottomAppBarPosition": {"description": "A setting for the position of the floating action button in the bottom app bar demo."}, "bottomAppBarPositionDockedEnd": "Docked - End", "@bottomAppBarPositionDockedEnd": {"description": "A setting for the position of the floating action button in the bottom app bar that docks the button in the bar and aligns it at the end."}, "bottomAppBarPositionDockedCenter": "Docked - Center", "@bottomAppBarPositionDockedCenter": {"description": "A setting for the position of the floating action button in the bottom app bar that docks the button in the bar and aligns it in the center."}, "bottomAppBarPositionFloatingEnd": "Floating - End", "@bottomAppBarPositionFloatingEnd": {"description": "A setting for the position of the floating action button in the bottom app bar that places the button above the bar and aligns it at the end."}, "bottomAppBarPositionFloatingCenter": "Floating - Center", "@bottomAppBarPositionFloatingCenter": {"description": "A setting for the position of the floating action button in the bottom app bar that places the button above the bar and aligns it in the center."}, "demoBannerTitle": "Banner", "@demoBannerTitle": {"description": "Title for the material banner component demo."}, "demoBannerSubtitle": "Displaying a banner within a list", "@demoBannerSubtitle": {"description": "Subtitle for the material banner component demo."}, "demoBannerDescription": "A banner displays an important, succinct message, and provides actions for users to address (or dismiss the banner). A user action is required for it to be dismissed.", "@demoBannerDescription": {"description": "Description for the material banner component demo."}, "demoBottomNavigationTitle": "Bottom navigation", "@demoBottomNavigationTitle": {"description": "Title for the material bottom navigation component demo."}, "demoBottomNavigationSubtitle": "Bottom navigation with cross-fading views", "@demoBottomNavigationSubtitle": {"description": "Subtitle for the material bottom navigation component demo."}, "demoBottomNavigationPersistentLabels": "Persistent labels", "@demoBottomNavigationPersistentLabels": {"description": "Option title for bottom navigation with persistent labels."}, "demoBottomNavigationSelectedLabel": "Selected label", "@demoBottomNavigationSelectedLabel": {"description": "Option title for bottom navigation with only a selected label."}, "demoBottomNavigationDescription": "Bottom navigation bars display three to five destinations at the bottom of a screen. Each destination is represented by an icon and an optional text label. When a bottom navigation icon is tapped, the user is taken to the top-level navigation destination associated with that icon.", "@demoBottomNavigationDescription": {"description": "Description for the material bottom navigation component demo."}, "demoButtonTitle": "Buttons", "@demoButtonTitle": {"description": "Title for the material buttons component demo."}, "demoButtonSubtitle": "Text, elevated, outlined, and more", "@demoButtonSubtitle": {"description": "Subtitle for the material buttons component demo."}, "demoTextButtonTitle": "Text But<PERSON>", "@demoTextButtonTitle": {"description": "Title for the text button component demo."}, "demoTextButtonDescription": "A text button displays an ink splash on press but does not lift. Use text buttons on toolbars, in dialogs and inline with padding", "@demoTextButtonDescription": {"description": "Description for the text button component demo."}, "demoElevatedButtonTitle": "Elevated <PERSON><PERSON>", "@demoElevatedButtonTitle": {"description": "Title for the elevated button component demo."}, "demoElevatedButtonDescription": "Elevated buttons add dimension to mostly flat layouts. They emphasize functions on busy or wide spaces.", "@demoElevatedButtonDescription": {"description": "Description for the elevated button component demo."}, "demoOutlinedButtonTitle": "Outlined <PERSON><PERSON>", "@demoOutlinedButtonTitle": {"description": "Title for the outlined button component demo."}, "demoOutlinedButtonDescription": "Outlined buttons become opaque and elevate when pressed. They are often paired with raised buttons to indicate an alternative, secondary action.", "@demoOutlinedButtonDescription": {"description": "Description for the outlined button component demo."}, "demoToggleButtonTitle": "Toggle <PERSON>", "@demoToggleButtonTitle": {"description": "Title for the toggle buttons component demo."}, "demoToggleButtonDescription": "Toggle buttons can be used to group related options. To emphasize groups of related toggle buttons, a group should share a common container", "@demoToggleButtonDescription": {"description": "Description for the toggle buttons component demo."}, "demoFloatingButtonTitle": "Floating Action Button", "@demoFloatingButtonTitle": {"description": "Title for the floating action button component demo."}, "demoFloatingButtonDescription": "A floating action button is a circular icon button that hovers over content to promote a primary action in the application.", "@demoFloatingButtonDescription": {"description": "Description for the floating action button component demo."}, "demoCardTitle": "Cards", "@demoCardTitle": {"description": "Title for the material cards component demo."}, "demoCardSubtitle": "Baseline cards with rounded corners", "@demoCardSubtitle": {"description": "Subtitle for the material cards component demo."}, "demoChipTitle": "Chips", "@demoChipTitle": {"description": "Title for the material chips component demo."}, "demoCardDescription": "A card is a sheet of Material used to represent some related information, for example an album, a geographical location, a meal, contact details, etc.", "@demoCardDescription": {"description": "Description for the material cards component demo."}, "demoChipSubtitle": "Compact elements that represent an input, attribute, or action", "@demoChipSubtitle": {"description": "Subtitle for the material chips component demo."}, "demoActionChipTitle": "Action Chip", "@demoActionChipTitle": {"description": "Title for the action chip component demo."}, "demoActionChipDescription": "Action chips are a set of options which trigger an action related to primary content. Action chips should appear dynamically and contextually in a UI.", "@demoActionChipDescription": {"description": "Description for the action chip component demo."}, "demoChoiceChipTitle": "Choice Chip", "@demoChoiceChipTitle": {"description": "Title for the choice chip component demo."}, "demoChoiceChipDescription": "Choice chips represent a single choice from a set. Choice chips contain related descriptive text or categories.", "@demoChoiceChipDescription": {"description": "Description for the choice chip component demo."}, "demoFilterChipTitle": "Filter Chip", "@demoFilterChipTitle": {"description": "Title for the filter chip component demo."}, "demoFilterChipDescription": "Filter chips use tags or descriptive words as a way to filter content.", "@demoFilterChipDescription": {"description": "Description for the filter chip component demo."}, "demoInputChipTitle": "Input Chip", "@demoInputChipTitle": {"description": "Title for the input chip component demo."}, "demoInputChipDescription": "Input chips represent a complex piece of information, such as an entity (person, place, or thing) or conversational text, in a compact form.", "@demoInputChipDescription": {"description": "Description for the input chip component demo."}, "demoDataTableTitle": "Data Tables", "@demoDataTableTitle": {"description": "Title for the material data table component demo."}, "demoDataTableSubtitle": "Rows and columns of information", "@demoDataTableSubtitle": {"description": "Subtitle for the material data table component demo."}, "demoDataTableDescription": "Data tables display information in a grid-like format of rows and columns. They organize information in a way that's easy to scan, so that users can look for patterns and insights.", "@demoDataTableDescription": {"description": "Description for the material data table component demo."}, "dataTableHeader": "Nutrition", "@dataTableHeader": {"description": "Header for the data table component demo about nutrition."}, "dataTableColumnDessert": "<PERSON><PERSON><PERSON> (1 serving)", "@dataTableColumnDessert": {"description": "Column header for desserts."}, "dataTableColumnCalories": "Calories", "@dataTableColumnCalories": {"description": "Column header for number of calories."}, "dataTableColumnFat": "Fat (g)", "@dataTableColumnFat": {"description": "Column header for number of grams of fat."}, "dataTableColumnCarbs": "Carbs (g)", "@dataTableColumnCarbs": {"description": "Column header for number of grams of carbs."}, "dataTableColumnProtein": "<PERSON><PERSON> (g)", "@dataTableColumnProtein": {"description": "Column header for number of grams of protein."}, "dataTableColumnSodium": "Sodium (mg)", "@dataTableColumnSodium": {"description": "Column header for number of milligrams of sodium."}, "dataTableColumnCalcium": "Calcium (%)", "@dataTableColumnCalcium": {"description": "Column header for daily percentage of calcium."}, "dataTableColumnIron": "Iron (%)", "@dataTableColumnIron": {"description": "Column header for daily percentage of iron."}, "dataTableRowFrozenYogurt": "Frozen yogurt", "@dataTableRowFrozenYogurt": {"description": "Column row for frozen yogurt."}, "dataTableRowIceCreamSandwich": "Ice cream sandwich", "@dataTableRowIceCreamSandwich": {"description": "Column row for Ice cream sandwich."}, "dataTableRowEclair": "Eclair", "@dataTableRowEclair": {"description": "Column row for Eclair."}, "dataTableRowCupcake": "Cupcake", "@dataTableRowCupcake": {"description": "Column row for Cupcake."}, "dataTableRowGingerbread": "Gingerbread", "@dataTableRowGingerbread": {"description": "Column row for Gingerbread."}, "dataTableRowJellyBean": "Jelly bean", "@dataTableRowJellyBean": {"description": "Column row for Jelly bean."}, "dataTableRowLollipop": "Lollipop", "@dataTableRowLollipop": {"description": "Column row for Lollipop."}, "dataTableRowHoneycomb": "Honeycomb", "@dataTableRowHoneycomb": {"description": "Column row for Honeycomb."}, "dataTableRowDonut": "Donut", "@dataTableRowDonut": {"description": "Column row for <PERSON><PERSON>."}, "dataTableRowApplePie": "Apple pie", "@dataTableRowApplePie": {"description": "Column row for Apple pie."}, "dataTableRowWithSugar": "{value} with sugar", "@dataTableRowWithSugar": {"description": "A dessert with sugar on it. The parameter is some type of dessert.", "placeholders": {"value": {"example": "Apple pie"}}}, "dataTableRowWithHoney": "{value} with honey", "@dataTableRowWithHoney": {"description": "A dessert with honey on it. The parameter is some type of dessert.", "placeholders": {"value": {"example": "Apple pie"}}}, "demoDialogTitle": "Dialogs", "@demoDialogTitle": {"description": "Title for the material dialog component demo."}, "demoDialogSubtitle": "Simple, alert, and fullscreen", "@demoDialogSubtitle": {"description": "Subtitle for the material dialog component demo."}, "demoAlertDialogTitle": "<PERSON><PERSON>", "@demoAlertDialogTitle": {"description": "Title for the alert dialog component demo."}, "demoAlertDialogDescription": "An alert dialog informs the user about situations that require acknowledgement. An alert dialog has an optional title and an optional list of actions.", "@demoAlertDialogDescription": {"description": "Description for the alert dialog component demo."}, "demoAlertTitleDialogTitle": "<PERSON><PERSON> With Title", "@demoAlertTitleDialogTitle": {"description": "Title for the alert dialog with title component demo."}, "demoSimpleDialogTitle": "Simple", "@demoSimpleDialogTitle": {"description": "Title for the simple dialog component demo."}, "demoSimpleDialogDescription": "A simple dialog offers the user a choice between several options. A simple dialog has an optional title that is displayed above the choices.", "@demoSimpleDialogDescription": {"description": "Description for the simple dialog component demo."}, "demoDividerTitle": "Divider", "@demoDividerTitle": {"description": "Title for the divider component demo."}, "demoDividerSubtitle": "A divider is a thin line that groups content in lists and layouts.", "@demoDividerSubtitle": {"description": "Subtitle for the divider component demo."}, "demoDividerDescription": "Dividers can be used in lists, drawers, and elsewhere to separate content.", "@demoDividerDescription": {"description": "Description for the divider component demo."}, "demoVerticalDividerTitle": "Vertical Divider", "@demoVerticalDividerTitle": {"description": "Title for the vertical divider component demo."}, "demoGridListsTitle": "Grid Lists", "@demoGridListsTitle": {"description": "Title for the grid lists component demo."}, "demoGridListsSubtitle": "Row and column layout", "@demoGridListsSubtitle": {"description": "Subtitle for the grid lists component demo."}, "demoGridListsDescription": "Grid Lists are best suited for presenting homogeneous data, typically images. Each item in a grid list is called a tile.", "@demoGridListsDescription": {"description": "Description for the grid lists component demo."}, "demoGridListsImageOnlyTitle": "Image only", "@demoGridListsImageOnlyTitle": {"description": "Title for the grid lists image-only component demo."}, "demoGridListsHeaderTitle": "With header", "@demoGridListsHeaderTitle": {"description": "Title for the grid lists component demo with headers on each tile."}, "demoGridListsFooterTitle": "With footer", "@demoGridListsFooterTitle": {"description": "Title for the grid lists component demo with footers on each tile."}, "demoSlidersTitle": "Sliders", "@demoSlidersTitle": {"description": "Title for the sliders component demo."}, "demoSlidersSubtitle": "Widgets for selecting a value by swiping", "@demoSlidersSubtitle": {"description": "Short description for the sliders component demo."}, "demoSlidersDescription": "Sliders reflect a range of values along a bar, from which users may select a single value. They are ideal for adjusting settings such as volume, brightness, or applying image filters.", "@demoSlidersDescription": {"description": "Description for the sliders demo."}, "demoRangeSlidersTitle": "Range Sliders", "@demoRangeSlidersTitle": {"description": "Title for the range sliders component demo."}, "demoRangeSlidersDescription": "Sliders reflect a range of values along a bar. They can have icons on both ends of the bar that reflect a range of values. They are ideal for adjusting settings such as volume, brightness, or applying image filters.", "@demoRangeSlidersDescription": {"description": "Description for the range sliders demo."}, "demoCustomSlidersTitle": "Custom Sliders", "@demoCustomSlidersTitle": {"description": "Title for the custom sliders component demo."}, "demoCustomSlidersDescription": "Sliders reflect a range of values along a bar, from which users may select a single value or range of values. The sliders can be themed and customized.", "@demoCustomSlidersDescription": {"description": "Description for the custom sliders demo."}, "demoSlidersContinuousWithEditableNumericalValue": "Continuous with Editable Numerical Value", "@demoSlidersContinuousWithEditableNumericalValue": {"description": "Text to describe a slider has a continuous value with an editable numerical value."}, "demoSlidersDiscrete": "Discrete", "@demoSlidersDiscrete": {"description": "Text to describe that we have a slider with discrete values."}, "demoSlidersDiscreteSliderWithCustomTheme": "Discrete Slider with Custom Theme", "@demoSlidersDiscreteSliderWithCustomTheme": {"description": "Text to describe that we have a slider with discrete values and a custom theme. "}, "demoSlidersContinuousRangeSliderWithCustomTheme": "Continuous Range Slider with Custom Theme", "@demoSlidersContinuousRangeSliderWithCustomTheme": {"description": "Text to describe that we have a range slider with continuous values and a custom theme. "}, "demoSlidersContinuous": "Continuous", "@demoSlidersContinuous": {"description": "Text to describe that we have a slider with continuous values."}, "demoSlidersEditableNumericalValue": "Editable numerical value", "@demoSlidersEditableNumericalValue": {"description": "Label for input field that has an editable numerical value."}, "demoMenuTitle": "<PERSON><PERSON>", "@demoMenuTitle": {"description": "Title for the menu component demo."}, "demoContextMenuTitle": "Context menu", "@demoContextMenuTitle": {"description": "Title for the context menu component demo."}, "demoSectionedMenuTitle": "Sectioned menu", "@demoSectionedMenuTitle": {"description": "Title for the sectioned menu component demo."}, "demoSimpleMenuTitle": "Simple menu", "@demoSimpleMenuTitle": {"description": "Title for the simple menu component demo."}, "demoChecklistMenuTitle": "Checklist menu", "@demoChecklistMenuTitle": {"description": "Title for the checklist menu component demo."}, "demoMenuSubtitle": "Menu buttons and simple menus", "@demoMenuSubtitle": {"description": "Short description for the menu component demo."}, "demoMenuDescription": "A menu displays a list of choices on a temporary surface. They appear when users interact with a button, action, or other control.", "@demoMenuDescription": {"description": "Description for the menu demo."}, "demoMenuItemValueOne": "Menu item one", "@demoMenuItemValueOne": {"description": "The first item in a menu."}, "demoMenuItemValueTwo": "Menu item two", "@demoMenuItemValueTwo": {"description": "The second item in a menu."}, "demoMenuItemValueThree": "Menu item three", "@demoMenuItemValueThree": {"description": "The third item in a menu."}, "demoMenuOne": "One", "@demoMenuOne": {"description": "The number one."}, "demoMenuTwo": "Two", "@demoMenuTwo": {"description": "The number two."}, "demoMenuThree": "Three", "@demoMenuThree": {"description": "The number three."}, "demoMenuFour": "Four", "@demoMenuFour": {"description": "The number four."}, "demoMenuAnItemWithAContextMenuButton": "An item with a context menu", "@demoMenuAnItemWithAContextMenuButton": {"description": "Label next to a button that opens a menu. A menu displays a list of choices on a temporary surface. Used as an example in a demo."}, "demoMenuContextMenuItemOne": "Context menu item one", "@demoMenuContextMenuItemOne": {"description": "Text label for a context menu item. A menu displays a list of choices on a temporary surface. Used as an example in a demo."}, "demoMenuADisabledMenuItem": "Disabled menu item", "@demoMenuADisabledMenuItem": {"description": "Text label for a disabled menu item. A menu displays a list of choices on a temporary surface. Used as an example in a demo."}, "demoMenuContextMenuItemThree": "Context menu item three", "@demoMenuContextMenuItemThree": {"description": "Text label for a context menu item three. A menu displays a list of choices on a temporary surface. Used as an example in a demo."}, "demoMenuAnItemWithASectionedMenu": "An item with a sectioned menu", "@demoMenuAnItemWithASectionedMenu": {"description": "Label next to a button that opens a sectioned menu . A menu displays a list of choices on a temporary surface. Used as an example in a demo."}, "demoMenuPreview": "Preview", "@demoMenuPreview": {"description": "Button to preview content."}, "demoMenuShare": "Share", "@demoMenuShare": {"description": "Button to share content."}, "demoMenuGetLink": "Get link", "@demoMenuGetLink": {"description": "Button to get link for content."}, "demoMenuRemove": "Remove", "@demoMenuRemove": {"description": "Button to remove content."}, "demoMenuSelected": "Selected: {value}", "@demoMenuSelected": {"description": "A text to show what value was selected.", "placeholders": {"value": {"example": "1"}}}, "demoMenuChecked": "Checked: {value}", "@demoMenuChecked": {"description": "A text to show what value was checked.", "placeholders": {"value": {"example": "1"}}}, "demoNavigationDrawerTitle": "Navigation Drawer", "@demoNavigationDrawerTitle": {"description": "Title for the material drawer component demo."}, "demoNavigationDrawerSubtitle": "Displaying a drawer within appbar", "@demoNavigationDrawerSubtitle": {"description": "Subtitle for the material drawer component demo."}, "demoNavigationDrawerDescription": "A Material Design panel that slides in horizontally from the edge of the screen to show navigation links in an application.", "@demoNavigationDrawerDescription": {"description": "Description for the material drawer component demo."}, "demoNavigationDrawerUserName": "User Name", "@demoNavigationDrawerUserName": {"description": "Demo username for navigation drawer."}, "demoNavigationDrawerUserEmail": "<EMAIL>", "@demoNavigationDrawerUserEmail": {"description": "Demo email for navigation drawer."}, "demoNavigationDrawerToPageOne": "Item One", "@demoNavigationDrawerToPageOne": {"description": "Drawer <PERSON><PERSON>."}, "demoNavigationDrawerToPageTwo": "Item Two", "@demoNavigationDrawerToPageTwo": {"description": "Drawer <PERSON><PERSON>."}, "demoNavigationDrawerText": "Swipe from the edge or tap the upper-left icon to see the drawer", "@demoNavigationDrawerText": {"description": "Description to open navigation drawer."}, "demoNavigationRailTitle": "Navigation Rail", "@demoNavigationRailTitle": {"description": "Title for the material Navigation Rail component demo."}, "demoNavigationRailSubtitle": "Displaying a Navigation Rail within an app", "@demoNavigationRailSubtitle": {"description": "Subtitle for the material Navigation Rail component demo."}, "demoNavigationRailDescription": "A material widget that is meant to be displayed at the left or right of an app to navigate between a small number of views, typically between three and five.", "@demoNavigationRailDescription": {"description": "Description for the material Navigation Rail component demo."}, "demoNavigationRailFirst": "First", "@demoNavigationRailFirst": {"description": "Navigation Rail destination first label."}, "demoNavigationRailSecond": "Second", "@demoNavigationRailSecond": {"description": "Navigation Rail destination second label."}, "demoNavigationRailThird": "Third", "@demoNavigationRailThird": {"description": "Navigation Rail destination Third label."}, "demoMenuAnItemWithASimpleMenu": "An item with a simple menu", "@demoMenuAnItemWithASimpleMenu": {"description": "Label next to a button that opens a simple menu. A menu displays a list of choices on a temporary surface. Used as an example in a demo."}, "demoMenuAnItemWithAChecklistMenu": "An item with a checklist menu", "@demoMenuAnItemWithAChecklistMenu": {"description": "Label next to a button that opens a checklist menu. A menu displays a list of choices on a temporary surface. Used as an example in a demo."}, "demoFullscreenDialogTitle": "Fullscreen", "@demoFullscreenDialogTitle": {"description": "Title for the fullscreen dialog component demo."}, "demoFullscreenDialogDescription": "The fullscreenDialog property specifies whether the incoming page is a fullscreen modal dialog", "@demoFullscreenDialogDescription": {"description": "Description for the fullscreen dialog component demo."}, "demoCupertinoActivityIndicatorTitle": "Activity indicator", "@demoCupertinoActivityIndicatorTitle": {"description": "Title for the cupertino activity indicator component demo."}, "demoCupertinoActivityIndicatorSubtitle": "iOS-style activity indicators", "@demoCupertinoActivityIndicatorSubtitle": {"description": "Subtitle for the cupertino activity indicator component demo."}, "demoCupertinoActivityIndicatorDescription": "An iOS-style activity indicator that spins clockwise.", "@demoCupertinoActivityIndicatorDescription": {"description": "Description for the cupertino activity indicator component demo."}, "demoCupertinoButtonsTitle": "Buttons", "@demoCupertinoButtonsTitle": {"description": "Title for the cupertino buttons component demo."}, "demoCupertinoButtonsSubtitle": "iOS-style buttons", "@demoCupertinoButtonsSubtitle": {"description": "Subtitle for the cupertino buttons component demo."}, "demoCupertinoButtonsDescription": "An iOS-style button. It takes in text and/or an icon that fades out and in on touch. May optionally have a background.", "@demoCupertinoButtonsDescription": {"description": "Description for the cupertino buttons component demo."}, "demoCupertinoContextMenuTitle": "Context Menu", "@demoCupertinoContextMenuTitle": {"description": "Title for the cupertino context menu component demo."}, "demoCupertinoContextMenuSubtitle": "iOS-style context menu", "@demoCupertinoContextMenuSubtitle": {"description": "Subtitle for the cupertino context menu component demo."}, "demoCupertinoContextMenuDescription": "An iOS-style full screen contextual menu that appears when an element is long-pressed.", "@demoCupertinoContextMenuDescription": {"description": "Description for the cupertino context menu component demo."}, "demoCupertinoContextMenuActionOne": "Action one", "@demoCupertinoContextMenuActionOne": {"description": "Context menu list item one"}, "demoCupertinoContextMenuActionTwo": "Action two", "@demoCupertinoContextMenuActionTwo": {"description": "Context menu list item two"}, "demoCupertinoContextMenuActionText": "Tap and hold the Flutter logo to see the context menu.", "@demoCupertinoContextMenuActionText": {"description": "Context menu text."}, "demoCupertinoAlertsTitle": "<PERSON><PERSON><PERSON>", "@demoCupertinoAlertsTitle": {"description": "Title for the cupertino alerts component demo."}, "demoCupertinoAlertsSubtitle": "iOS-style alert dialogs", "@demoCupertinoAlertsSubtitle": {"description": "Subtitle for the cupertino alerts component demo."}, "demoCupertinoAlertTitle": "<PERSON><PERSON>", "@demoCupertinoAlertTitle": {"description": "Title for the cupertino alert component demo."}, "demoCupertinoAlertDescription": "An alert dialog informs the user about situations that require acknowledgement. An alert dialog has an optional title, optional content, and an optional list of actions. The title is displayed above the content and the actions are displayed below the content.", "@demoCupertinoAlertDescription": {"description": "Description for the cupertino alert component demo."}, "demoCupertinoAlertWithTitleTitle": "<PERSON><PERSON> With Title", "@demoCupertinoAlertWithTitleTitle": {"description": "Title for the cupertino alert with title component demo."}, "demoCupertinoAlertButtonsTitle": "<PERSON><PERSON>", "@demoCupertinoAlertButtonsTitle": {"description": "Title for the cupertino alert with buttons component demo."}, "demoCupertinoAlertButtonsOnlyTitle": "<PERSON><PERSON> Only", "@demoCupertinoAlertButtonsOnlyTitle": {"description": "Title for the cupertino alert buttons only component demo."}, "demoCupertinoActionSheetTitle": "Action Sheet", "@demoCupertinoActionSheetTitle": {"description": "Title for the cupertino action sheet component demo."}, "demoCupertinoActionSheetDescription": "An action sheet is a specific style of alert that presents the user with a set of two or more choices related to the current context. An action sheet can have a title, an additional message, and a list of actions.", "@demoCupertinoActionSheetDescription": {"description": "Description for the cupertino action sheet component demo."}, "demoCupertinoNavigationBarTitle": "Navigation bar", "@demoCupertinoNavigationBarTitle": {"description": "Title for the cupertino navigation bar component demo."}, "demoCupertinoNavigationBarSubtitle": "iOS-style navigation bar", "@demoCupertinoNavigationBarSubtitle": {"description": "Subtitle for the cupertino navigation bar component demo."}, "demoCupertinoNavigationBarDescription": "An iOS-styled navigation bar. The navigation bar is a toolbar that minimally consists of a page title, in the middle of the toolbar.", "@demoCupertinoNavigationBarDescription": {"description": "Description for the cupertino navigation bar component demo."}, "demoCupertinoPickerTitle": "Pickers", "@demoCupertinoPickerTitle": {"description": "Title for the cupertino pickers component demo."}, "demoCupertinoPickerSubtitle": "iOS-style pickers", "@demoCupertinoPickerSubtitle": {"description": "Subtitle for the cupertino pickers component demo."}, "demoCupertinoPickerDescription": "An iOS-style picker widget that can be used to select strings, dates, times, or both date and time.", "@demoCupertinoPickerDescription": {"description": "Description for the cupertino pickers component demo."}, "demoCupertinoPickerTimer": "Timer", "@demoCupertinoPickerTimer": {"description": "Label to open a countdown timer picker."}, "demoCupertinoPicker": "Picker", "@demoCupertinoPicker": {"description": "Label to open an iOS picker."}, "demoCupertinoPickerDate": "Date", "@demoCupertinoPickerDate": {"description": "Label to open a date picker."}, "demoCupertinoPickerTime": "Time", "@demoCupertinoPickerTime": {"description": "Label to open a time picker."}, "demoCupertinoPickerDateTime": "Date and Time", "@demoCupertinoPickerDateTime": {"description": "Label to open a date and time picker."}, "demoCupertinoPullToRefreshTitle": "Pull to refresh", "@demoCupertinoPullToRefreshTitle": {"description": "Title for the cupertino pull-to-refresh component demo."}, "demoCupertinoPullToRefreshSubtitle": "iOS-style pull to refresh control", "@demoCupertinoPullToRefreshSubtitle": {"description": "Subtitle for the cupertino pull-to-refresh component demo."}, "demoCupertinoPullToRefreshDescription": "A widget implementing the iOS-style pull to refresh content control.", "@demoCupertinoPullToRefreshDescription": {"description": "Description for the cupertino pull-to-refresh component demo."}, "demoCupertinoSegmentedControlTitle": "Segmented control", "@demoCupertinoSegmentedControlTitle": {"description": "Title for the cupertino segmented control component demo."}, "demoCupertinoSegmentedControlSubtitle": "iOS-style segmented control", "@demoCupertinoSegmentedControlSubtitle": {"description": "Subtitle for the cupertino segmented control component demo."}, "demoCupertinoSegmentedControlDescription": "Used to select between a number of mutually exclusive options. When one option in the segmented control is selected, the other options in the segmented control cease to be selected.", "@demoCupertinoSegmentedControlDescription": {"description": "Description for the cupertino segmented control component demo."}, "demoCupertinoSliderTitle": "Slide<PERSON>", "@demoCupertinoSliderTitle": {"description": "Title for the cupertino slider component demo."}, "demoCupertinoSliderSubtitle": "iOS-style slider", "@demoCupertinoSliderSubtitle": {"description": "Subtitle for the cupertino slider component demo."}, "demoCupertinoSliderDescription": "A slider can be used to select from either a continuous or a discrete set of values.", "@demoCupertinoSliderDescription": {"description": "Description for the cupertino slider component demo."}, "demoCupertinoSliderContinuous": "Continuous: {value}", "@demoCupertinoSliderContinuous": {"description": "A label for a continuous slider that indicates what value it is set to.", "placeholders": {"value": {"example": "1"}}}, "demoCupertinoSliderDiscrete": "Discrete: {value}", "@demoCupertinoSliderDiscrete": {"description": "A label for a discrete slider that indicates what value it is set to.", "placeholders": {"value": {"example": "1"}}}, "demoCupertinoSwitchSubtitle": "iOS-style switch", "@demoCupertinoSwitchSubtitle": {"description": "Subtitle for the cupertino switch component demo."}, "demoCupertinoSwitchDescription": "A switch is used to toggle the on/off state of a single setting.", "@demoCupertinoSwitchDescription": {"description": "Description for the cupertino switch component demo."}, "demoCupertinoTabBarTitle": "Tab bar", "@demoCupertinoTabBarTitle": {"description": "Title for the cupertino bottom tab bar demo."}, "demoCupertinoTabBarSubtitle": "iOS-style bottom tab bar", "@demoCupertinoTabBarSubtitle": {"description": "Subtitle for the cupertino bottom tab bar demo."}, "demoCupertinoTabBarDescription": "An iOS-style bottom navigation tab bar. Displays multiple tabs with one tab being active, the first tab by default.", "@demoCupertinoTabBarDescription": {"description": "Description for the cupertino bottom tab bar demo."}, "cupertinoTabBarHomeTab": "Home", "@cupertinoTabBarHomeTab": {"description": "Title for the home tab in the bottom tab bar demo."}, "cupertinoTabBarChatTab": "Cha<PERSON>", "@cupertinoTabBarChatTab": {"description": "Title for the chat tab in the bottom tab bar demo."}, "cupertinoTabBarProfileTab": "Profile", "@cupertinoTabBarProfileTab": {"description": "Title for the profile tab in the bottom tab bar demo."}, "demoCupertinoTextFieldTitle": "Text fields", "@demoCupertinoTextFieldTitle": {"description": "Title for the cupertino text field demo."}, "demoCupertinoTextFieldSubtitle": "iOS-style text fields", "@demoCupertinoTextFieldSubtitle": {"description": "Subtitle for the cupertino text field demo."}, "demoCupertinoTextFieldDescription": "A text field lets the user enter text, either with a hardware keyboard or with an onscreen keyboard.", "@demoCupertinoTextFieldDescription": {"description": "Description for the cupertino text field demo."}, "demoCupertinoTextFieldPIN": "PIN", "@demoCupertinoTextFieldPIN": {"description": "The placeholder for a text field where a user would enter their PIN number."}, "demoCupertinoSearchTextFieldTitle": "Search text field", "@demoCupertinoSearchTextFieldTitle": {"description": "Title for the cupertino search text field demo."}, "demoCupertinoSearchTextFieldSubtitle": "iOS-style search text field", "@demoCupertinoSearchTextFieldSubtitle": {"description": "Subtitle for the cupertino search text field demo."}, "demoCupertinoSearchTextFieldDescription": "A search text field that lets the user search by entering text, and that can offer and filter suggestions.", "@demoCupertinoSearchTextFieldDescription": {"description": "Description for the cupertino search text field demo."}, "demoCupertinoSearchTextFieldPlaceholder": "Enter some text", "@demoCupertinoSearchTextFieldPlaceholder": {"description": "The placeholder for a search text field demo."}, "demoCupertinoScrollbarTitle": "Sc<PERSON><PERSON>", "@demoCupertinoScrollbarTitle": {"description": "Title for the cupertino scrollbar demo."}, "demoCupertinoScrollbarSubtitle": "iOS-style scrollbar", "@demoCupertinoScrollbarSubtitle": {"description": "Subtitle for the cupertino scrollbar demo."}, "demoCupertinoScrollbarDescription": "A scrollbar that wraps the given child", "@demoCupertinoScrollbarDescription": {"description": "Description for the cupertino scrollbar demo."}, "demoMotionTitle": "Motion", "@demoMotionTitle": {"description": "Title for the motion demo."}, "demoMotionSubtitle": "All of the predefined transition patterns", "@demoMotionSubtitle": {"description": "Subtitle for the motion demo."}, "demoContainerTransformDemoInstructions": "Cards, Lists & FAB", "@demoContainerTransformDemoInstructions": {"description": "Instructions for the container transform demo located in the app bar."}, "demoSharedXAxisDemoInstructions": "Next and Back Buttons", "@demoSharedXAxisDemoInstructions": {"description": "Instructions for the shared x axis demo located in the app bar."}, "demoSharedYAxisDemoInstructions": "Sort by \"Recently Played\"", "@demoSharedYAxisDemoInstructions": {"description": "Instructions for the shared y axis demo located in the app bar."}, "demoSharedZAxisDemoInstructions": "Settings icon button", "@demoSharedZAxisDemoInstructions": {"description": "Instructions for the shared z axis demo located in the app bar."}, "demoFadeThroughDemoInstructions": "Bottom navigation", "@demoFadeThroughDemoInstructions": {"description": "Instructions for the fade through demo located in the app bar."}, "demoFadeScaleDemoInstructions": "Modal and FAB", "@demoFadeScaleDemoInstructions": {"description": "Instructions for the fade scale demo located in the app bar."}, "demoContainerTransformTitle": "Container Transform", "@demoContainerTransformTitle": {"description": "Title for the container transform demo."}, "demoContainerTransformDescription": "The container transform pattern is designed for transitions between UI elements that include a container. This pattern creates a visible connection between two UI elements", "@demoContainerTransformDescription": {"description": "Description for the container transform demo."}, "demoContainerTransformModalBottomSheetTitle": "Fade mode", "@demoContainerTransformModalBottomSheetTitle": {"description": "Title for the container transform modal bottom sheet."}, "demoContainerTransformTypeFade": "FADE", "@demoContainerTransformTypeFade": {"description": "Description for container transform fade type setting."}, "demoContainerTransformTypeFadeThrough": "FADE THROUGH", "@demoContainerTransformTypeFadeThrough": {"description": "Description for container transform fade through type setting."}, "demoMotionPlaceholderTitle": "Title", "@demoMotionPlaceholderTitle": {"description": "The placeholder for the motion demos title properties."}, "demoMotionPlaceholderSubtitle": "Secondary text", "@demoMotionPlaceholderSubtitle": {"description": "The placeholder for the motion demos subtitle properties."}, "demoMotionSmallPlaceholderSubtitle": "Secondary", "@demoMotionSmallPlaceholderSubtitle": {"description": "The placeholder for the motion demos shortened subtitle properties."}, "demoMotionDetailsPageTitle": "Details Page", "@demoMotionDetailsPageTitle": {"description": "The title for the details page in the motion demos."}, "demoMotionListTileTitle": "List item", "@demoMotionListTileTitle": {"description": "The title for a list tile in the motion demos."}, "demoSharedAxisDescription": "The shared axis pattern is used for transitions between the UI elements that have a spatial or navigational relationship. This pattern uses a shared transformation on the x, y, or z axis to reinforce the relationship between elements.", "@demoSharedAxisDescription": {"description": "Description for the shared y axis demo."}, "demoSharedXAxisTitle": "Shared x-axis", "@demoSharedXAxisTitle": {"description": "Title for the shared x axis demo."}, "demoSharedXAxisBackButtonText": "BACK", "@demoSharedXAxisBackButtonText": {"description": "Button text for back button in the shared x axis demo."}, "demoSharedXAxisNextButtonText": "NEXT", "@demoSharedXAxisNextButtonText": {"description": "<PERSON><PERSON> text for the next button in the shared x axis demo."}, "demoSharedXAxisCoursePageTitle": "Streamline your courses", "@demoSharedXAxisCoursePageTitle": {"description": "Title for course selection page in the shared x axis demo."}, "demoSharedXAxisCoursePageSubtitle": "Bundled categories appear as groups in your feed. You can always change this later.", "@demoSharedXAxisCoursePageSubtitle": {"description": "Subtitle for course selection page in the shared x axis demo."}, "demoSharedXAxisArtsAndCraftsCourseTitle": "Arts & Crafts", "@demoSharedXAxisArtsAndCraftsCourseTitle": {"description": "Title for the Arts & Crafts course in the shared x axis demo."}, "demoSharedXAxisBusinessCourseTitle": "Business", "@demoSharedXAxisBusinessCourseTitle": {"description": "Title for the Business course in the shared x axis demo."}, "demoSharedXAxisIllustrationCourseTitle": "Illustration", "@demoSharedXAxisIllustrationCourseTitle": {"description": "Title for the Illustration course in the shared x axis demo."}, "demoSharedXAxisDesignCourseTitle": "Design", "@demoSharedXAxisDesignCourseTitle": {"description": "Title for the Design course in the shared x axis demo."}, "demoSharedXAxisCulinaryCourseTitle": "Culinary", "@demoSharedXAxisCulinaryCourseTitle": {"description": "Title for the Culinary course in the shared x axis demo."}, "demoSharedXAxisBundledCourseSubtitle": "Bundled", "@demoSharedXAxisBundledCourseSubtitle": {"description": "Subtitle for a bundled course in the shared x axis demo."}, "demoSharedXAxisIndividualCourseSubtitle": "Shown Individually", "@demoSharedXAxisIndividualCourseSubtitle": {"description": "Subtitle for a individual course in the shared x axis demo."}, "demoSharedXAxisSignInWelcomeText": "<PERSON>", "@demoSharedXAxisSignInWelcomeText": {"description": "Welcome text for sign in page in the shared x axis demo. David Park is a name and does not need to be translated."}, "demoSharedXAxisSignInSubtitleText": "Sign in with your account", "@demoSharedXAxisSignInSubtitleText": {"description": "Subtitle text for sign in page in the shared x axis demo."}, "demoSharedXAxisSignInTextFieldLabel": "Email or phone number", "@demoSharedXAxisSignInTextFieldLabel": {"description": "Label text for the sign in text field in the shared x axis demo."}, "demoSharedXAxisForgotEmailButtonText": "FORGOT EMAIL?", "@demoSharedXAxisForgotEmailButtonText": {"description": "<PERSON>ton text for the forgot email button in the shared x axis demo."}, "demoSharedXAxisCreateAccountButtonText": "CREATE ACCOUNT", "@demoSharedXAxisCreateAccountButtonText": {"description": "Button text for the create account button in the shared x axis demo."}, "demoSharedYAxisTitle": "Shared y-axis", "@demoSharedYAxisTitle": {"description": "Title for the shared y axis demo."}, "demoSharedYAxisAlbumCount": "268 albums", "@demoSharedYAxisAlbumCount": {"description": "Text for album count in the shared y axis demo."}, "demoSharedYAxisAlphabeticalSortTitle": "A-Z", "@demoSharedYAxisAlphabeticalSortTitle": {"description": "Title for alphabetical sorting type in the shared y axis demo."}, "demoSharedYAxisRecentSortTitle": "Recently played", "@demoSharedYAxisRecentSortTitle": {"description": "Title for recently played sorting type in the shared y axis demo."}, "demoSharedYAxisAlbumTileTitle": "Album", "@demoSharedYAxisAlbumTileTitle": {"description": "Title for an AlbumTile in the shared y axis demo."}, "demoSharedYAxisAlbumTileSubtitle": "Artist", "@demoSharedYAxisAlbumTileSubtitle": {"description": "Subtitle for an AlbumTile in the shared y axis demo."}, "demoSharedYAxisAlbumTileDurationUnit": "min", "@demoSharedYAxisAlbumTileDurationUnit": {"description": "Duration unit for an AlbumTile in the shared y axis demo."}, "demoSharedZAxisTitle": "Shared z-axis", "@demoSharedZAxisTitle": {"description": "Title for the shared z axis demo."}, "demoSharedZAxisSettingsPageTitle": "Settings", "@demoSharedZAxisSettingsPageTitle": {"description": "Title for the settings page in the shared z axis demo."}, "demoSharedZAxisBurgerRecipeTitle": "Burger", "@demoSharedZAxisBurgerRecipeTitle": {"description": "Title for burger recipe tile in the shared z axis demo."}, "demoSharedZAxisBurgerRecipeDescription": "Burger recipe", "@demoSharedZAxisBurgerRecipeDescription": {"description": "Subtitle for the burger recipe tile in the shared z axis demo."}, "demoSharedZAxisSandwichRecipeTitle": "Sandwich", "@demoSharedZAxisSandwichRecipeTitle": {"description": "Title for sandwich recipe tile in the shared z axis demo."}, "demoSharedZAxisSandwichRecipeDescription": "Sandwich recipe", "@demoSharedZAxisSandwichRecipeDescription": {"description": "Subtitle for the sandwich recipe tile in the shared z axis demo."}, "demoSharedZAxisDessertRecipeTitle": "Dessert", "@demoSharedZAxisDessertRecipeTitle": {"description": "Title for dessert recipe tile in the shared z axis demo."}, "demoSharedZAxisDessertRecipeDescription": "Dessert recipe", "@demoSharedZAxisDessertRecipeDescription": {"description": "Subtitle for the dessert recipe tile in the shared z axis demo."}, "demoSharedZAxisShrimpPlateRecipeTitle": "<PERSON>mp", "@demoSharedZAxisShrimpPlateRecipeTitle": {"description": "Title for shrimp plate recipe tile in the shared z axis demo."}, "demoSharedZAxisShrimpPlateRecipeDescription": "Shrimp plate recipe", "@demoSharedZAxisShrimpPlateRecipeDescription": {"description": "Subtitle for the shrimp plate recipe tile in the shared z axis demo."}, "demoSharedZAxisCrabPlateRecipeTitle": "<PERSON><PERSON>", "@demoSharedZAxisCrabPlateRecipeTitle": {"description": "Title for crab plate recipe tile in the shared z axis demo."}, "demoSharedZAxisCrabPlateRecipeDescription": "Crab plate recipe", "@demoSharedZAxisCrabPlateRecipeDescription": {"description": "Subtitle for the crab plate recipe tile in the shared z axis demo."}, "demoSharedZAxisBeefSandwichRecipeTitle": "<PERSON><PERSON>", "@demoSharedZAxisBeefSandwichRecipeTitle": {"description": "Title for beef sandwich recipe tile in the shared z axis demo."}, "demoSharedZAxisBeefSandwichRecipeDescription": "Beef <PERSON> recipe", "@demoSharedZAxisBeefSandwichRecipeDescription": {"description": "Subtitle for the beef sandwich recipe tile in the shared z axis demo."}, "demoSharedZAxisSavedRecipesListTitle": "Saved <PERSON><PERSON><PERSON>", "@demoSharedZAxisSavedRecipesListTitle": {"description": "Title for list of saved recipes in the shared z axis demo."}, "demoSharedZAxisProfileSettingLabel": "Profile", "@demoSharedZAxisProfileSettingLabel": {"description": "Text label for profile setting tile in the shared z axis demo."}, "demoSharedZAxisNotificationSettingLabel": "Notifications", "@demoSharedZAxisNotificationSettingLabel": {"description": "Text label for notifications setting tile in the shared z axis demo."}, "demoSharedZAxisPrivacySettingLabel": "Privacy", "@demoSharedZAxisPrivacySettingLabel": {"description": "Text label for the privacy setting tile in the shared z axis demo."}, "demoSharedZAxisHelpSettingLabel": "Help", "@demoSharedZAxisHelpSettingLabel": {"description": "Text label for the help setting tile in the shared z axis demo."}, "demoFadeThroughTitle": "Fade through", "@demoFadeThroughTitle": {"description": "Title for the fade through demo."}, "demoFadeThroughDescription": "The fade through pattern is used for transitions between UI elements that do not have a strong relationship to each other.", "@demoFadeThroughDescription": {"description": "Description for the fade through demo."}, "demoFadeThroughAlbumsDestination": "Albums", "@demoFadeThroughAlbumsDestination": {"description": "Text for albums bottom navigation bar destination in the fade through demo."}, "demoFadeThroughPhotosDestination": "Photos", "@demoFadeThroughPhotosDestination": {"description": "Text for photos bottom navigation bar destination in the fade through demo."}, "demoFadeThroughSearchDestination": "Search", "@demoFadeThroughSearchDestination": {"description": "Text for search bottom navigation bar destination in the fade through demo."}, "demoFadeThroughTextPlaceholder": "123 photos", "@demoFadeThroughTextPlaceholder": {"description": "Placeholder for example card title in the fade through demo."}, "demoFadeScaleTitle": "Fade", "@demoFadeScaleTitle": {"description": "Title for the fade scale demo."}, "demoFadeScaleDescription": "The fade pattern is used for UI elements that enter or exit within the bounds of the screen, such as a dialog that fades in the center of the screen.", "@demoFadeScaleDescription": {"description": "Description for the fade scale demo."}, "demoFadeScaleShowAlertDialogButton": "SHOW MODAL", "@demoFadeScaleShowAlertDialogButton": {"description": "<PERSON><PERSON> text to show alert dialog in the fade scale demo."}, "demoFadeScaleShowFabButton": "SHOW FAB", "@demoFadeScaleShowFabButton": {"description": "<PERSON><PERSON> text to show fab in the fade scale demo."}, "demoFadeScaleHideFabButton": "HIDE FAB", "@demoFadeScaleHideFabButton": {"description": "<PERSON><PERSON> text to hide fab in the fade scale demo."}, "demoFadeScaleAlertDialogHeader": "<PERSON><PERSON>", "@demoFadeScaleAlertDialogHeader": {"description": "Generic header for alert dialog in the fade scale demo."}, "demoFadeScaleAlertDialogCancelButton": "CANCEL", "@demoFadeScaleAlertDialogCancelButton": {"description": "Button text for alert dialog cancel button in the fade scale demo."}, "demoFadeScaleAlertDialogDiscardButton": "DISCARD", "@demoFadeScaleAlertDialogDiscardButton": {"description": "<PERSON>ton text for alert dialog discard button in the fade scale demo."}, "demoColorsTitle": "Colors", "@demoColorsTitle": {"description": "Title for the colors demo."}, "demoColorsSubtitle": "All of the predefined colors", "@demoColorsSubtitle": {"description": "Subtitle for the colors demo."}, "demoColorsDescription": "Color and color swatch constants which represent Material Design's color palette.", "@demoColorsDescription": {"description": "Description for the colors demo. Material Design should remain capitalized."}, "demoTypographyTitle": "Typography", "@demoTypographyTitle": {"description": "Title for the typography demo."}, "demoTypographySubtitle": "All of the predefined text styles", "@demoTypographySubtitle": {"description": "Subtitle for the typography demo."}, "demoTypographyDescription": "Definitions for the various typographical styles found in Material Design.", "@demoTypographyDescription": {"description": "Description for the typography demo. Material Design should remain capitalized."}, "demo2dTransformationsTitle": "2D transformations", "@demo2dTransformationsTitle": {"description": "Title for the 2D transformations demo."}, "demo2dTransformationsSubtitle": "Pan and zoom", "@demo2dTransformationsSubtitle": {"description": "Subtitle for the 2D transformations demo."}, "demo2dTransformationsDescription": "Tap to edit tiles, and use gestures to move around the scene. Drag to pan and pinch with two fingers to zoom. Press the reset button to return to the starting orientation.", "@demo2dTransformationsDescription": {"description": "Description for the 2D transformations demo."}, "demo2dTransformationsResetTooltip": "Reset transformations", "@demo2dTransformationsResetTooltip": {"description": "Tooltip for a button to reset the transformations (scale, translation) for the 2D transformations demo."}, "demo2dTransformationsEditTooltip": "Edit tile", "@demo2dTransformationsEditTooltip": {"description": "Tooltip for a button to edit a tile."}, "buttonText": "BUTTON", "@buttonText": {"description": "Text for a generic button."}, "demoBottomSheetTitle": "Bottom sheet", "@demoBottomSheetTitle": {"description": "Title for bottom sheet demo."}, "demoBottomSheetSubtitle": "Persistent and modal bottom sheets", "@demoBottomSheetSubtitle": {"description": "Description for bottom sheet demo."}, "demoBottomSheetPersistentTitle": "Persistent bottom sheet", "@demoBottomSheetPersistentTitle": {"description": "Title for persistent bottom sheet demo."}, "demoBottomSheetPersistentDescription": "A persistent bottom sheet shows information that supplements the primary content of the app. A persistent bottom sheet remains visible even when the user interacts with other parts of the app.", "@demoBottomSheetPersistentDescription": {"description": "Description for persistent bottom sheet demo."}, "demoBottomSheetModalTitle": "Modal bottom sheet", "@demoBottomSheetModalTitle": {"description": "Title for modal bottom sheet demo."}, "demoBottomSheetModalDescription": "A modal bottom sheet is an alternative to a menu or a dialog and prevents the user from interacting with the rest of the app.", "@demoBottomSheetModalDescription": {"description": "Description for modal bottom sheet demo."}, "demoBottomSheetAddLabel": "Add", "@demoBottomSheetAddLabel": {"description": "Semantic label for add icon."}, "demoBottomSheetButtonText": "SHOW BOTTOM SHEET", "@demoBottomSheetButtonText": {"description": "Button text to show bottom sheet."}, "demoBottomSheetHeader": "Header", "@demoBottomSheetHeader": {"description": "Generic header placeholder."}, "demoBottomSheetItem": "Item {value}", "@demoBottomSheetItem": {"description": "Generic item placeholder.", "placeholders": {"value": {"example": "1"}}}, "demoListsTitle": "Lists", "@demoListsTitle": {"description": "Title for lists demo."}, "demoListsSubtitle": "Scrolling list layouts", "@demoListsSubtitle": {"description": "Subtitle for lists demo."}, "demoListsDescription": "A single fixed-height row that typically contains some text as well as a leading or trailing icon.", "@demoListsDescription": {"description": "Description for lists demo. This describes what a single row in a list consists of."}, "demoOneLineListsTitle": "One Line", "@demoOneLineListsTitle": {"description": "Title for lists demo with only one line of text per row."}, "demoTwoLineListsTitle": "Two Lines", "@demoTwoLineListsTitle": {"description": "Title for lists demo with two lines of text per row."}, "demoListsSecondary": "Secondary text", "@demoListsSecondary": {"description": "Text that appears in the second line of a list item."}, "demoProgressIndicatorTitle": "Progress indicators", "@demoProgressIndicatorTitle": {"description": "Title for progress indicators demo."}, "demoProgressIndicatorSubtitle": "Linear, circular, indeterminate", "@demoProgressIndicatorSubtitle": {"description": "Subtitle for progress indicators demo."}, "demoCircularProgressIndicatorTitle": "Circular Progress Indicator", "@demoCircularProgressIndicatorTitle": {"description": "Title for circular progress indicator demo."}, "demoCircularProgressIndicatorDescription": "A Material Design circular progress indicator, which spins to indicate that the application is busy.", "@demoCircularProgressIndicatorDescription": {"description": "Description for circular progress indicator demo."}, "demoLinearProgressIndicatorTitle": "Linear Progress Indicator", "@demoLinearProgressIndicatorTitle": {"description": "Title for linear progress indicator demo."}, "demoLinearProgressIndicatorDescription": "A Material Design linear progress indicator, also known as a progress bar.", "@demoLinearProgressIndicatorDescription": {"description": "Description for linear progress indicator demo."}, "demoPickersTitle": "Pickers", "@demoPickersTitle": {"description": "Title for pickers demo."}, "demoPickersSubtitle": "Date and time selection", "@demoPickersSubtitle": {"description": "Subtitle for pickers demo."}, "demoDatePickerTitle": "Date Picker", "@demoDatePickerTitle": {"description": "Title for date picker demo."}, "demoDatePickerDescription": "Shows a dialog containing a Material Design date picker.", "@demoDatePickerDescription": {"description": "Description for date picker demo."}, "demoTimePickerTitle": "Time Picker", "@demoTimePickerTitle": {"description": "Title for time picker demo."}, "demoTimePickerDescription": "Shows a dialog containing a Material Design time picker.", "@demoTimePickerDescription": {"description": "Description for time picker demo."}, "demoDateRangePickerTitle": "Date Range Picker", "@demoDateRangePickerTitle": {"description": "Title for date range picker demo."}, "demoDateRangePickerDescription": "Shows a dialog containing a Material Design date range picker.", "@demoDateRangePickerDescription": {"description": "Description for date range picker demo."}, "demoPickersShowPicker": "SHOW PICKER", "@demoPickersShowPicker": {"description": "<PERSON><PERSON> text to show the date or time picker in the demo."}, "demoTabsTitle": "Tabs", "@demoTabsTitle": {"description": "Title for tabs demo."}, "demoTabsScrollingTitle": "Scrolling", "@demoTabsScrollingTitle": {"description": "Title for tabs demo with a tab bar that scrolls."}, "demoTabsNonScrollingTitle": "Non-scrolling", "@demoTabsNonScrollingTitle": {"description": "Title for tabs demo with a tab bar that doesn't scroll."}, "demoTabsSubtitle": "Tabs with independently scrollable views", "@demoTabsSubtitle": {"description": "Subtitle for tabs demo."}, "demoTabsDescription": "Tabs organize content across different screens, data sets, and other interactions.", "@demoTabsDescription": {"description": "Description for tabs demo."}, "demoSnackbarsTitle": "Snackbars", "@demoSnackbarsTitle": {"description": "Title for snackbars demo."}, "demoSnackbarsSubtitle": "Snackbars show messages at the bottom of the screen", "@demoSnackbarsSubtitle": {"description": "Subtitle for snackbars demo."}, "demoSnackbarsDescription": "Snackbars inform users of a process that an app has performed or will perform. They appear temporarily, towards the bottom of the screen. They shouldn't interrupt the user experience, and they don't require user input to disappear.", "@demoSnackbarsDescription": {"description": "Description for snackbars demo."}, "demoSnackbarsButtonLabel": "SHOW A SNACKBAR", "@demoSnackbarsButtonLabel": {"description": "Label for button to show a snackbar."}, "demoSnackbarsText": "This is a snackbar.", "@demoSnackbarsText": {"description": "Text to show on a snackbar."}, "demoSnackbarsActionButtonLabel": "ACTION", "@demoSnackbarsActionButtonLabel": {"description": "Label for action button text on the snackbar."}, "demoSnackbarsAction": "You pressed the snackbar action.", "@demoSnackbarsAction": {"description": "Text that appears when you press on a snackbars' action."}, "demoSelectionControlsTitle": "Selection controls", "@demoSelectionControlsTitle": {"description": "Title for selection controls demo."}, "demoSelectionControlsSubtitle": "Checkboxes, radio buttons, and switches", "@demoSelectionControlsSubtitle": {"description": "Subtitle for selection controls demo."}, "demoSelectionControlsCheckboxTitle": "Checkbox", "@demoSelectionControlsCheckboxTitle": {"description": "Title for the checkbox (selection controls) demo."}, "demoSelectionControlsCheckboxDescription": "Checkboxes allow the user to select multiple options from a set. A normal checkbox's value is true or false and a tristate checkbox's value can also be null.", "@demoSelectionControlsCheckboxDescription": {"description": "Description for the checkbox (selection controls) demo."}, "demoSelectionControlsRadioTitle": "Radio", "@demoSelectionControlsRadioTitle": {"description": "Title for the radio button (selection controls) demo."}, "demoSelectionControlsRadioDescription": "Radio buttons allow the user to select one option from a set. Use radio buttons for exclusive selection if you think that the user needs to see all available options side-by-side.", "@demoSelectionControlsRadioDescription": {"description": "Description for the radio button (selection controls) demo."}, "demoSelectionControlsSwitchTitle": "Switch", "@demoSelectionControlsSwitchTitle": {"description": "Title for the switches (selection controls) demo."}, "demoSelectionControlsSwitchDescription": "On/off switches toggle the state of a single settings option. The option that the switch controls, as well as the state it's in, should be made clear from the corresponding inline label.", "@demoSelectionControlsSwitchDescription": {"description": "Description for the switches (selection controls) demo."}, "demoBottomTextFieldsTitle": "Text fields", "@demoBottomTextFieldsTitle": {"description": "Title for text fields demo."}, "demoTextFieldTitle": "Text fields", "@demoTextFieldTitle": {"description": "Title for text fields demo."}, "demoTextFieldSubtitle": "Single line of editable text and numbers", "@demoTextFieldSubtitle": {"description": "Description for text fields demo."}, "demoTextFieldDescription": "Text fields allow users to enter text into a UI. They typically appear in forms and dialogs.", "@demoTextFieldDescription": {"description": "Description for text fields demo."}, "demoTextFieldShowPasswordLabel": "Show password", "@demoTextFieldShowPasswordLabel": {"description": "Label for show password icon."}, "demoTextFieldHidePasswordLabel": "Hide password", "@demoTextFieldHidePasswordLabel": {"description": "Label for hide password icon."}, "demoTextFieldFormErrors": "Please fix the errors in red before submitting.", "@demoTextFieldFormErrors": {"description": "Text that shows up on form errors."}, "demoTextFieldNameRequired": "Name is required.", "@demoTextFieldNameRequired": {"description": "Shows up as submission error if name is not given in the form."}, "demoTextFieldOnlyAlphabeticalChars": "Please enter only alphabetical characters.", "@demoTextFieldOnlyAlphabeticalChars": {"description": "Error that shows if non-alphabetical characters are given."}, "demoTextFieldEnterUSPhoneNumber": "(###) ###-#### - Enter a US phone number.", "@demoTextFieldEnterUSPhoneNumber": {"description": "Error that shows up if non-valid non-US phone number is given."}, "demoTextFieldEnterPassword": "Please enter a password.", "@demoTextFieldEnterPassword": {"description": "Error that shows up if password is not given."}, "demoTextFieldPasswordsDoNotMatch": "The passwords don't match", "@demoTextFieldPasswordsDoNotMatch": {"description": "Error that shows up, if the re-typed password does not match the already given password."}, "demoTextFieldWhatDoPeopleCallYou": "What do people call you?", "@demoTextFieldWhatDoPeopleCallYou": {"description": "Placeholder for name field in form."}, "demoTextFieldNameField": "Name*", "@demoTextFieldNameField": {"description": "The label for a name input field that is required (hence the star)."}, "demoTextFieldWhereCanWeReachYou": "Where can we reach you?", "@demoTextFieldWhereCanWeReachYou": {"description": "Placeholder for when entering a phone number in a form."}, "demoTextFieldPhoneNumber": "Phone number*", "@demoTextFieldPhoneNumber": {"description": "The label for a phone number input field that is required (hence the star)."}, "demoTextFieldYourEmailAddress": "Your email address", "@demoTextFieldYourEmailAddress": {"description": "The label for an email address input field."}, "demoTextFieldEmail": "Email", "@demoTextFieldEmail": {"description": "The label for an email address input field"}, "demoTextFieldTellUsAboutYourself": "Tell us about yourself (e.g., write down what you do or what hobbies you have)", "@demoTextFieldTellUsAboutYourself": {"description": "The placeholder text for biography/life story input field."}, "demoTextFieldKeepItShort": "Keep it short, this is just a demo.", "@demoTextFieldKeepItShort": {"description": "Helper text for biography/life story input field."}, "demoTextFieldLifeStory": "Life story", "@demoTextFieldLifeStory": {"description": "The label for biography/life story input field."}, "demoTextFieldSalary": "Salary", "@demoTextFieldSalary": {"description": "The label for salary input field."}, "demoTextFieldUSD": "USD", "@demoTextFieldUSD": {"description": "US currency, used as suffix in input field for salary."}, "demoTextFieldNoMoreThan": "No more than 8 characters.", "@demoTextFieldNoMoreThan": {"description": "Helper text for password input field."}, "demoTextFieldPassword": "Password*", "@demoTextFieldPassword": {"description": "Label for password input field, that is required (hence the star)."}, "demoTextFieldRetypePassword": "Re-type password*", "@demoTextFieldRetypePassword": {"description": "Label for repeat password input field."}, "demoTextFieldSubmit": "SUBMIT", "@demoTextFieldSubmit": {"description": "The submit button text for form."}, "demoTextFieldNameHasPhoneNumber": "{name} phone number is {phoneNumber}", "@demoTextFieldNameHasPhoneNumber": {"description": "Text that shows up when valid phone number and name is submitted in form.", "placeholders": {"name": {"example": "<PERSON>"}, "phoneNumber": {"phoneNumber": "+****************"}}}, "demoTextFieldRequiredField": "* indicates required field", "@demoTextFieldRequiredField": {"description": "Helper text to indicate that * means that it is a required field."}, "demoTooltipTitle": "Tooltips", "@demoTooltipTitle": {"description": "Title for tooltip demo."}, "demoTooltipSubtitle": "Short message displayed on long press or hover", "@demoTooltipSubtitle": {"description": "Subtitle for tooltip demo."}, "demoTooltipDescription": "Tooltips provide text labels that help explain the function of a button or other user interface action. Tooltips display informative text when users hover over, focus on, or long press an element.", "@demoTooltipDescription": {"description": "Description for tooltip demo."}, "demoTooltipInstructions": "Long press or hover to display the tooltip.", "@demoTooltipInstructions": {"description": "Instructions for how to trigger a tooltip in the tooltip demo."}, "bottomNavigationCommentsTab": "Comments", "@bottomNavigationCommentsTab": {"description": "Title for Comments tab of bottom navigation."}, "bottomNavigationCalendarTab": "Calendar", "@bottomNavigationCalendarTab": {"description": "Title for Calendar tab of bottom navigation."}, "bottomNavigationAccountTab": "Account", "@bottomNavigationAccountTab": {"description": "Title for Account tab of bottom navigation."}, "bottomNavigationAlarmTab": "Alarm", "@bottomNavigationAlarmTab": {"description": "Title for Alarm tab of bottom navigation."}, "bottomNavigationCameraTab": "Camera", "@bottomNavigationCameraTab": {"description": "Title for Camera tab of bottom navigation."}, "bottomNavigationContentPlaceholder": "Placeholder for {title} tab", "@bottomNavigationContentPlaceholder": {"description": "Accessibility label for the content placeholder in the bottom navigation demo", "placeholders": {"title": {"example": "Account"}}}, "buttonTextCreate": "Create", "@buttonTextCreate": {"description": "Tooltip text for a create button."}, "dialogSelectedOption": "You selected: \"{value}\"", "@dialogSelectedOption": {"description": "Message displayed after an option is selected from a dialog", "placeholders": {"value": {"example": "AGREE"}}}, "chipTurnOnLights": "Turn on lights", "@chipTurnOnLights": {"description": "A chip component to turn on the lights."}, "chipSmall": "Small", "@chipSmall": {"description": "A chip component to select a small size."}, "chipMedium": "Medium", "@chipMedium": {"description": "A chip component to select a medium size."}, "chipLarge": "Large", "@chipLarge": {"description": "A chip component to select a large size."}, "chipElevator": "Elevator", "@chipElevator": {"description": "A chip component to filter selection by elevators."}, "chipWasher": "<PERSON>her", "@chipWasher": {"description": "A chip component to filter selection by washers."}, "chipFireplace": "Fireplace", "@chipFireplace": {"description": "A chip component to filter selection by fireplaces."}, "chipBiking": "Biking", "@chipBiking": {"description": "A chip component to that indicates a biking selection."}, "demo": "Demo", "@demo": {"description": "Used in the title of the demos."}, "bottomAppBar": "Bottom app bar", "@bottomAppBar": {"description": "Used as semantic label for a BottomAppBar."}, "loading": "Loading", "@loading": {"description": "Indicates the loading process."}, "dialogDiscardTitle": "Discard draft?", "@dialogDiscardTitle": {"description": "Alert dialog message to discard draft."}, "dialogLocationTitle": "Use Google's location service?", "@dialogLocationTitle": {"description": "Alert dialog title to use location services."}, "dialogLocationDescription": "Let Google help apps determine location. This means sending anonymous location data to Google, even when no apps are running.", "@dialogLocationDescription": {"description": "Alert dialog description to use location services."}, "dialogCancel": "CANCEL", "@dialogCancel": {"description": "Alert dialog cancel option."}, "dialogDiscard": "DISCARD", "@dialogDiscard": {"description": "Alert dialog discard option."}, "dialogDisagree": "DISAGREE", "@dialogDisagree": {"description": "Alert dialog disagree option."}, "dialogAgree": "AGREE", "@dialogAgree": {"description": "Alert dialog agree option."}, "dialogSetBackup": "Set backup account", "@dialogSetBackup": {"description": "Alert dialog title for setting a backup account."}, "dialogAddAccount": "Add account", "@dialogAddAccount": {"description": "Alert dialog option for adding an account."}, "dialogShow": "SHOW DIALOG", "@dialogShow": {"description": "Button text to display a dialog."}, "dialogFullscreenTitle": "Full Screen Dialog", "@dialogFullscreenTitle": {"description": "Title for full screen dialog demo."}, "dialogFullscreenSave": "SAVE", "@dialogFullscreenSave": {"description": "Save button for full screen dialog demo."}, "dialogFullscreenDescription": "A full screen dialog demo", "@dialogFullscreenDescription": {"description": "Description for full screen dialog demo."}, "cupertinoButton": "<PERSON><PERSON>", "@cupertinoButton": {"description": "Button text for a generic iOS-style button."}, "cupertinoButtonWithBackground": "With Background", "@cupertinoButtonWithBackground": {"description": "<PERSON>ton text for a iOS-style button with a filled background."}, "cupertinoAlertCancel": "Cancel", "@cupertinoAlertCancel": {"description": "iOS-style alert cancel option."}, "cupertinoAlertDiscard": "Discard", "@cupertinoAlertDiscard": {"description": "iOS-style alert discard option."}, "cupertinoAlertLocationTitle": "Allow \"Maps\" to access your location while you are using the app?", "@cupertinoAlertLocationTitle": {"description": "iOS-style alert title for location permission."}, "cupertinoAlertLocationDescription": "Your current location will be displayed on the map and used for directions, nearby search results, and estimated travel times.", "@cupertinoAlertLocationDescription": {"description": "iOS-style alert description for location permission."}, "cupertinoAlertAllow": "Allow", "@cupertinoAlertAllow": {"description": "iOS-style alert allow option."}, "cupertinoAlertDontAllow": "Don't Allow", "@cupertinoAlertDontAllow": {"description": "iOS-style alert don't allow option."}, "cupertinoAlertFavoriteDessert": "Select Favorite Dessert", "@cupertinoAlertFavoriteDessert": {"description": "iOS-style alert title for selecting favorite dessert."}, "cupertinoAlertDessertDescription": "Please select your favorite type of dessert from the list below. Your selection will be used to customize the suggested list of eateries in your area.", "@cupertinoAlertDessertDescription": {"description": "iOS-style alert description for selecting favorite dessert."}, "cupertinoAlertCheesecake": "Cheesecake", "@cupertinoAlertCheesecake": {"description": "iOS-style alert cheesecake option."}, "cupertinoAlertTiramisu": "Tiramisu", "@cupertinoAlertTiramisu": {"description": "iOS-style alert tiramisu option."}, "cupertinoAlertApplePie": "Apple Pie", "@cupertinoAlertApplePie": {"description": "iOS-style alert apple pie option."}, "cupertinoAlertChocolateBrownie": "Chocolate Brownie", "@cupertinoAlertChocolateBrownie": {"description": "iOS-style alert chocolate brownie option."}, "cupertinoShowAlert": "Show Alert", "@cupertinoShowAlert": {"description": "Button text to show iOS-style alert."}, "colorsRed": "RED", "@colorsRed": {"description": "Tab title for the color red."}, "colorsPink": "PINK", "@colorsPink": {"description": "Tab title for the color pink."}, "colorsPurple": "PURPLE", "@colorsPurple": {"description": "Tab title for the color purple."}, "colorsDeepPurple": "DEEP PURPLE", "@colorsDeepPurple": {"description": "Tab title for the color deep purple."}, "colorsIndigo": "INDIGO", "@colorsIndigo": {"description": "Tab title for the color indigo."}, "colorsBlue": "BLUE", "@colorsBlue": {"description": "Tab title for the color blue."}, "colorsLightBlue": "LIGHT BLUE", "@colorsLightBlue": {"description": "Tab title for the color light blue."}, "colorsCyan": "CYAN", "@colorsCyan": {"description": "Tab title for the color cyan."}, "colorsTeal": "TEAL", "@colorsTeal": {"description": "Tab title for the color teal."}, "colorsGreen": "GREEN", "@colorsGreen": {"description": "Tab title for the color green."}, "colorsLightGreen": "LIGHT GREEN", "@colorsLightGreen": {"description": "Tab title for the color light green."}, "colorsLime": "LIME", "@colorsLime": {"description": "Tab title for the color lime."}, "colorsYellow": "YELLOW", "@colorsYellow": {"description": "Tab title for the color yellow."}, "colorsAmber": "AMBER", "@colorsAmber": {"description": "Tab title for the color amber."}, "colorsOrange": "ORANGE", "@colorsOrange": {"description": "Tab title for the color orange."}, "colorsDeepOrange": "DEEP ORANGE", "@colorsDeepOrange": {"description": "Tab title for the color deep orange."}, "colorsBrown": "BROWN", "@colorsBrown": {"description": "Tab title for the color brown."}, "colorsGrey": "GREY", "@colorsGrey": {"description": "Tab title for the color grey."}, "colorsBlueGrey": "BLUE GREY", "@colorsBlueGrey": {"description": "Tab title for the color blue grey."}, "placeChennai": "Chennai", "@placeChennai": {"description": "Title for Chennai location."}, "placeTanjore": "<PERSON><PERSON><PERSON>", "@placeTanjore": {"description": "Title for Tanjore location."}, "placeChettinad": "Chettinad", "@placeChettinad": {"description": "Title for Chettinad location."}, "placePondicherry": "Pondicherry", "@placePondicherry": {"description": "Title for Pondicherry location."}, "placeFlowerMarket": "Flower Market", "@placeFlowerMarket": {"description": "Title for Flower Market location."}, "placeBronzeWorks": "Bronze Works", "@placeBronzeWorks": {"description": "Title for Bronze Works location."}, "placeMarket": "Market", "@placeMarket": {"description": "Title for Market location."}, "placeThanjavurTemple": "Thanjavur Temple", "@placeThanjavurTemple": {"description": "Title for Thanjavur Temple location."}, "placeSaltFarm": "Salt Farm", "@placeSaltFarm": {"description": "Title for Salt Farm location."}, "placeScooters": "Scooters", "@placeScooters": {"description": "Title for image of people riding on scooters."}, "placeSilkMaker": "Silk Maker", "@placeSilkMaker": {"description": "Title for an image of a silk maker."}, "placeLunchPrep": "Lunch Prep", "@placeLunchPrep": {"description": "Title for an image of preparing lunch."}, "placeBeach": "Beach", "@placeBeach": {"description": "Title for Beach location."}, "placeFisherman": "Fisherman", "@placeFisherman": {"description": "Title for an image of a fisherman."}, "starterAppTitle": "Starter app", "@starterAppTitle": {"description": "The title and name for the starter app."}, "starterAppDescription": "A responsive starter layout", "@starterAppDescription": {"description": "The description for the starter app."}, "starterAppGenericButton": "BUTTON", "@starterAppGenericButton": {"description": "Generic placeholder for button."}, "starterAppTooltipAdd": "Add", "@starterAppTooltipAdd": {"description": "Tooltip on add icon."}, "starterAppTooltipFavorite": "Favorite", "@starterAppTooltipFavorite": {"description": "Tooltip on favorite icon."}, "starterAppTooltipShare": "Share", "@starterAppTooltipShare": {"description": "Tooltip on share icon."}, "starterAppTooltipSearch": "Search", "@starterAppTooltipSearch": {"description": "Tooltip on search icon."}, "starterAppGenericTitle": "Title", "@starterAppGenericTitle": {"description": "Generic placeholder for title in app bar."}, "starterAppGenericSubtitle": "Subtitle", "@starterAppGenericSubtitle": {"description": "Generic placeholder for subtitle in drawer."}, "starterAppGenericHeadline": "Headline", "@starterAppGenericHeadline": {"description": "Generic placeholder for headline in drawer."}, "starterAppGenericBody": "Body", "@starterAppGenericBody": {"description": "Generic placeholder for body text in drawer."}, "starterAppDrawerItem": "Item {value}", "@starterAppDrawerItem": {"description": "Generic placeholder drawer item.", "placeholders": {"value": {"example": "1"}}}, "shrineMenuCaption": "MENU", "@shrineMenuCaption": {"description": "Caption for a menu page."}, "shrineCategoryNameAll": "ALL", "@shrineCategoryNameAll": {"description": "A tab showing products from all categories."}, "shrineCategoryNameAccessories": "ACCESSORIES", "@shrineCategoryNameAccessories": {"description": "A category of products consisting of accessories (clothing items)."}, "shrineCategoryNameClothing": "CLOTHING", "@shrineCategoryNameClothing": {"description": "A category of products consisting of clothing."}, "shrineCategoryNameHome": "HOME", "@shrineCategoryNameHome": {"description": "A category of products consisting of items used at home."}, "shrineLogoutButtonCaption": "LOGOUT", "@shrineLogoutButtonCaption": {"description": "Label for a logout button."}, "shrineLoginUsernameLabel": "Username", "@shrineLoginUsernameLabel": {"description": "On the login screen, a label for a textfield for the user to input their username."}, "shrineLoginPasswordLabel": "Password", "@shrineLoginPasswordLabel": {"description": "On the login screen, a label for a textfield for the user to input their password."}, "shrineCancelButtonCaption": "CANCEL", "@shrineCancelButtonCaption": {"description": "On the login screen, the caption for a button to cancel login."}, "shrineNextButtonCaption": "NEXT", "@shrineNextButtonCaption": {"description": "On the login screen, the caption for a button to proceed login."}, "shrineCartPageCaption": "CART", "@shrineCartPageCaption": {"description": "Caption for a shopping cart page."}, "shrineProductQuantity": "Quantity: {quantity}", "@shrineProductQuantity": {"description": "A text showing the number of items for a specific product.", "placeholders": {"quantity": {"example": "3"}}}, "shrineProductPrice": "x {price}", "@shrineProductPrice": {"description": "A text showing the unit price of each product. Used as: 'Quantity: 3 x $129'. The currency will be handled by the formatter.", "placeholders": {"price": {"example": "$129"}}}, "shrineCartItemCount": "{quantity, plural, =0{NO ITEMS} =1{1 ITEM} other{{quantity} ITEMS}}", "@shrineCartItemCount": {"description": "A text showing the total number of items in the cart.", "placeholders": {"quantity": {"example": "3"}}}, "shrineCartClearButtonCaption": "CLEAR CART", "@shrineCartClearButtonCaption": {"description": "Caption for a button used to clear the cart."}, "shrineCartTotalCaption": "TOTAL", "@shrineCartTotalCaption": {"description": "Label for a text showing total price of the items in the cart."}, "shrineCartSubtotalCaption": "Subtotal:", "@shrineCartSubtotalCaption": {"description": "Label for a text showing the subtotal price of the items in the cart (excluding shipping and tax)."}, "shrineCartShippingCaption": "Shipping:", "@shrineCartShippingCaption": {"description": "Label for a text showing the shipping cost for the items in the cart."}, "shrineCartTaxCaption": "Tax:", "@shrineCartTaxCaption": {"description": "Label for a text showing the tax for the items in the cart."}, "shrineProductVagabondSack": "Vagabond sack", "@shrineProductVagabondSack": {"description": "Name of the product 'Vagabond sack'."}, "shrineProductStellaSunglasses": "Stella sunglasses", "@shrineProductStellaSunglasses": {"description": "Name of the product 'Stella sunglasses'."}, "shrineProductWhitneyBelt": "Whitney belt", "@shrineProductWhitneyBelt": {"description": "Name of the product 'Whitney belt'."}, "shrineProductGardenStrand": "Garden strand", "@shrineProductGardenStrand": {"description": "Name of the product 'Garden strand'."}, "shrineProductStrutEarrings": "<PERSON><PERSON><PERSON> earrings", "@shrineProductStrutEarrings": {"description": "Name of the product 'Strut earrings'."}, "shrineProductVarsitySocks": "Varsity socks", "@shrineProductVarsitySocks": {"description": "Name of the product 'Varsity socks'."}, "shrineProductWeaveKeyring": "Weave keyring", "@shrineProductWeaveKeyring": {"description": "Name of the product 'Weave keyring'."}, "shrineProductGatsbyHat": "Gatsby hat", "@shrineProductGatsbyHat": {"description": "Name of the product 'Gatsby hat'."}, "shrineProductShrugBag": "Shrug bag", "@shrineProductShrugBag": {"description": "Name of the product 'Shrug bag'."}, "shrineProductGiltDeskTrio": "Gilt desk trio", "@shrineProductGiltDeskTrio": {"description": "Name of the product 'Gilt desk trio'."}, "shrineProductCopperWireRack": "Copper wire rack", "@shrineProductCopperWireRack": {"description": "Name of the product 'Copper wire rack'."}, "shrineProductSootheCeramicSet": "Soothe ceramic set", "@shrineProductSootheCeramicSet": {"description": "Name of the product 'Soothe ceramic set'."}, "shrineProductHurrahsTeaSet": "Hurrahs tea set", "@shrineProductHurrahsTeaSet": {"description": "Name of the product 'Hurrahs tea set'."}, "shrineProductBlueStoneMug": "Blue stone mug", "@shrineProductBlueStoneMug": {"description": "Name of the product 'Blue stone mug'."}, "shrineProductRainwaterTray": "Rainwater tray", "@shrineProductRainwaterTray": {"description": "Name of the product 'Rainwater tray'."}, "shrineProductChambrayNapkins": "Chambray napkins", "@shrineProductChambrayNapkins": {"description": "Name of the product 'Chambray napkins'."}, "shrineProductSucculentPlanters": "Succulent planters", "@shrineProductSucculentPlanters": {"description": "Name of the product 'Succulent planters'."}, "shrineProductQuartetTable": "Quartet table", "@shrineProductQuartetTable": {"description": "Name of the product 'Quartet table'."}, "shrineProductKitchenQuattro": "Kitchen quattro", "@shrineProductKitchenQuattro": {"description": "Name of the product 'Kitchen quattro'."}, "shrineProductClaySweater": "Clay sweater", "@shrineProductClaySweater": {"description": "Name of the product 'Clay sweater'."}, "shrineProductSeaTunic": "Sea tunic", "@shrineProductSeaTunic": {"description": "Name of the product 'Sea tunic'."}, "shrineProductPlasterTunic": "Plaster tunic", "@shrineProductPlasterTunic": {"description": "Name of the product 'Plaster tunic'."}, "shrineProductWhitePinstripeShirt": "White pinstripe shirt", "@shrineProductWhitePinstripeShirt": {"description": "Name of the product 'White pinstripe shirt'."}, "shrineProductChambrayShirt": "Chambray shirt", "@shrineProductChambrayShirt": {"description": "Name of the product 'Chambray shirt'."}, "shrineProductSeabreezeSweater": "Seabreeze sweater", "@shrineProductSeabreezeSweater": {"description": "Name of the product 'Seabreeze sweater'."}, "shrineProductGentryJacket": "Gentry jacket", "@shrineProductGentryJacket": {"description": "Name of the product 'Gentry jacket'."}, "shrineProductNavyTrousers": "Navy trousers", "@shrineProductNavyTrousers": {"description": "Name of the product 'Navy trousers'."}, "shrineProductWalterHenleyWhite": "<PERSON> (white)", "@shrineProductWalterHenleyWhite": {"description": "Name of the product '<PERSON> hen<PERSON> (white)'."}, "shrineProductSurfAndPerfShirt": "Surf and perf shirt", "@shrineProductSurfAndPerfShirt": {"description": "Name of the product 'Surf and perf shirt'."}, "shrineProductGingerScarf": "Ginger scarf", "@shrineProductGingerScarf": {"description": "Name of the product 'Ginger scarf'."}, "shrineProductRamonaCrossover": "Ramona crossover", "@shrineProductRamonaCrossover": {"description": "Name of the product 'Ramona crossover'."}, "shrineProductClassicWhiteCollar": "Classic white collar", "@shrineProductClassicWhiteCollar": {"description": "Name of the product 'Classic white collar'."}, "shrineProductCeriseScallopTee": "<PERSON><PERSON> scallop tee", "@shrineProductCeriseScallopTee": {"description": "Name of the product 'Cerise scallop tee'."}, "shrineProductShoulderRollsTee": "Shoulder rolls tee", "@shrineProductShoulderRollsTee": {"description": "Name of the product 'Shoulder rolls tee'."}, "shrineProductGreySlouchTank": "Grey slouch tank", "@shrineProductGreySlouchTank": {"description": "Name of the product 'Grey slouch tank'."}, "shrineProductSunshirtDress": "Sunshirt dress", "@shrineProductSunshirtDress": {"description": "Name of the product '<PERSON>hir<PERSON> dress'."}, "shrineProductFineLinesTee": "Fine lines tee", "@shrineProductFineLinesTee": {"description": "Name of the product 'Fine lines tee'."}, "shrineTooltipSearch": "Search", "@shrineTooltipSearch": {"description": "The tooltip text for a search button. Also used as a semantic label, used by screen readers, such as TalkBack and VoiceOver."}, "shrineTooltipSettings": "Settings", "@shrineTooltipSettings": {"description": "The tooltip text for a settings button. Also used as a semantic label, used by screen readers, such as TalkBack and VoiceOver."}, "shrineTooltipOpenMenu": "Open menu", "@shrineTooltipOpenMenu": {"description": "The tooltip text for a menu button. Also used as a semantic label, used by screen readers, such as TalkBack and VoiceOver."}, "shrineTooltipCloseMenu": "Close menu", "@shrineTooltipCloseMenu": {"description": "The tooltip text for a button to close a menu. Also used as a semantic label, used by screen readers, such as TalkBack and VoiceOver."}, "shrineTooltipCloseCart": "Close cart", "@shrineTooltipCloseCart": {"description": "The tooltip text for a button to close the shopping cart page. Also used as a semantic label, used by screen readers, such as TalkBack and VoiceOver."}, "shrineScreenReaderCart": "{quantity, plural, =0{Shopping cart, no items} =1{Shopping cart, 1 item} other{Shopping cart, {quantity} items}}", "@shrineScreenReaderCart": {"description": "The description of a shopping cart button containing some products. Used by screen readers, such as TalkBack and VoiceOver.", "placeholders": {"quantity": {"example": "3"}}}, "shrineScreenReaderProductAddToCart": "Add to cart", "@shrineScreenReaderProductAddToCart": {"description": "An announcement made by screen readers, such as TalkBack and VoiceOver to indicate the action of a button for adding a product to the cart."}, "shrineScreenReaderRemoveProductButton": "Remove {product}", "@shrineScreenReaderRemoveProductButton": {"description": "A tooltip for a button to remove a product. This will be read by screen readers, such as TalkBack and VoiceOver when a product is added to the shopping cart.", "placeholders": {"product": {"example": "Ginger scarf"}}}, "shrineTooltipRemoveItem": "Remove item", "@shrineTooltipRemoveItem": {"description": "The tooltip text for a button to remove an item (a product) in a shopping cart. Also used as a semantic label, used by screen readers, such as TalkBack and VoiceOver."}, "craneFormDiners": "Diners", "@craneFormDiners": {"description": "Form field label to enter the number of diners."}, "craneFormDate": "Select Date", "@craneFormDate": {"description": "Form field label to select a date."}, "craneFormTime": "Select Time", "@craneFormTime": {"description": "Form field label to select a time."}, "craneFormLocation": "Select Location", "@craneFormLocation": {"description": "Form field label to select a location."}, "craneFormTravelers": "Travelers", "@craneFormTravelers": {"description": "Form field label to select the number of travellers."}, "craneFormOrigin": "Choose <PERSON>", "@craneFormOrigin": {"description": "Form field label to choose a travel origin."}, "craneFormDestination": "Choose Destination", "@craneFormDestination": {"description": "Form field label to choose a travel destination."}, "craneFormDates": "Select Dates", "@craneFormDates": {"description": "Form field label to select multiple dates."}, "craneHours": "{hours, plural, =1{1h} other{{hours}h}}", "@craneHours": {"description": "Generic text for an amount of hours, abbreviated to the shortest form. For example 1h. {hours} should remain untranslated.", "placeholders": {"hours": {"example": "1"}}}, "craneMinutes": "{minutes, plural, =1{1m} other{{minutes}m}}", "@craneMinutes": {"description": "Generic text for an amount of minutes, abbreviated to the shortest form. For example 15m. {minutes} should remain untranslated.", "placeholders": {"minutes": {"example": "15"}}}, "craneFlightDuration": "{hoursShortForm} {minutesShortForm}", "@craneFlightDuration": {"description": "A pattern to define the layout of a flight duration string. For example in English one might say 1h 15m. Translation should only rearrange the inputs. {hoursShortForm} would for example be replaced by 1h, already translated to the given locale. {minutesShortForm} would for example be replaced by 15m, already translated to the given locale.", "placeholders": {"hoursShortForm": {"example": "1h"}, "minutesShortForm": {"example": "15m"}}}, "craneFly": "FLY", "@craneFly": {"description": "Title for FLY tab."}, "craneSleep": "SLEEP", "@craneSleep": {"description": "Title for SLEEP tab."}, "craneEat": "EAT", "@craneEat": {"description": "Title for EAT tab."}, "craneFlySubhead": "Explore Flights by Destination", "@craneFlySubhead": {"description": "Subhead for FLY tab."}, "craneSleepSubhead": "Explore Properties by Destination", "@craneSleepSubhead": {"description": "Subhead for SLEEP tab."}, "craneEatSubhead": "Explore Restaurants by Destination", "@craneEatSubhead": {"description": "Subhead for EAT tab."}, "craneFlyStops": "{numberOfStops, plural, =0{Nonstop} =1{1 stop} other{{numberOfStops} stops}}", "@craneFlyStops": {"description": "Label indicating if a flight is nonstop or how many layovers it includes.", "placeholders": {"numberOfStops": {"example": "2"}}}, "craneSleepProperties": "{totalProperties, plural, =0{No Available Properties} =1{1 Available Properties} other{{totalProperties} Available Properties}}", "@craneSleepProperties": {"description": "Text indicating the number of available properties (temporary rentals). Always plural.", "placeholders": {"totalProperties": {"example": "100"}}}, "craneEatRestaurants": "{totalRestaurants, plural, =0{No Restaurants} =1{1 Restaurant} other{{totalRestaurants} Restaurants}}", "@craneEatRestaurants": {"description": "Text indicating the number of restaurants. Always plural.", "placeholders": {"totalRestaurants": {"example": "100"}}}, "craneFly0": "Aspen, United States", "@craneFly0": {"description": "Label for city."}, "craneFly1": "Big Sur, United States", "@craneFly1": {"description": "Label for city."}, "craneFly2": "Khumbu Valley, Nepal", "@craneFly2": {"description": "Label for city."}, "craneFly3": "Machu <PERSON>hu, Peru", "@craneFly3": {"description": "Label for city."}, "craneFly4": "Malé, Maldives", "@craneFly4": {"description": "Label for city."}, "craneFly5": "Vitznau, Switzerland", "@craneFly5": {"description": "Label for city."}, "craneFly6": "Mexico City, Mexico", "@craneFly6": {"description": "Label for city."}, "craneFly7": "Mount Rushmore, United States", "@craneFly7": {"description": "Label for city."}, "craneFly8": "Singapore", "@craneFly8": {"description": "Label for city."}, "craneFly9": "Havana, Cuba", "@craneFly9": {"description": "Label for city."}, "craneFly10": "Cairo, Egypt", "@craneFly10": {"description": "Label for city."}, "craneFly11": "Lisbon, Portugal", "@craneFly11": {"description": "Label for city."}, "craneFly12": "Napa, United States", "@craneFly12": {"description": "Label for city."}, "craneFly13": "Bali, Indonesia", "@craneFly13": {"description": "Label for city."}, "craneSleep0": "Malé, Maldives", "@craneSleep0": {"description": "Label for city."}, "craneSleep1": "Aspen, United States", "@craneSleep1": {"description": "Label for city."}, "craneSleep2": "Machu <PERSON>hu, Peru", "@craneSleep2": {"description": "Label for city."}, "craneSleep3": "Havana, Cuba", "@craneSleep3": {"description": "Label for city."}, "craneSleep4": "Vitznau, Switzerland", "@craneSleep4": {"description": "Label for city."}, "craneSleep5": "Big Sur, United States", "@craneSleep5": {"description": "Label for city."}, "craneSleep6": "Napa, United States", "@craneSleep6": {"description": "Label for city."}, "craneSleep7": "Porto, Portugal", "@craneSleep7": {"description": "Label for city."}, "craneSleep8": "Tulum, Mexico", "@craneSleep8": {"description": "Label for city."}, "craneSleep9": "Lisbon, Portugal", "@craneSleep9": {"description": "Label for city."}, "craneSleep10": "Cairo, Egypt", "@craneSleep10": {"description": "Label for city."}, "craneSleep11": "Taipei, Taiwan", "@craneSleep11": {"description": "Label for city."}, "craneEat0": "Naples, Italy", "@craneEat0": {"description": "Label for city."}, "craneEat1": "Dallas, United States", "@craneEat1": {"description": "Label for city."}, "craneEat2": "Córdoba, Argentina", "@craneEat2": {"description": "Label for city."}, "craneEat3": "Portland, United States", "@craneEat3": {"description": "Label for city."}, "craneEat4": "Paris, France", "@craneEat4": {"description": "Label for city."}, "craneEat5": "Seoul, South Korea", "@craneEat5": {"description": "Label for city."}, "craneEat6": "Seattle, United States", "@craneEat6": {"description": "Label for city."}, "craneEat7": "Nashville, United States", "@craneEat7": {"description": "Label for city."}, "craneEat8": "Atlanta, United States", "@craneEat8": {"description": "Label for city."}, "craneEat9": "Madrid, Spain", "@craneEat9": {"description": "Label for city."}, "craneEat10": "Lisbon, Portugal", "@craneEat10": {"description": "Label for city."}, "craneFly0SemanticLabel": "Chalet in a snowy landscape with evergreen trees", "@craneFly0SemanticLabel": {"description": "Semantic label for an image."}, "craneFly1SemanticLabel": "Tent in a field", "@craneFly1SemanticLabel": {"description": "Semantic label for an image."}, "craneFly2SemanticLabel": "Prayer flags in front of snowy mountain", "@craneFly2SemanticLabel": {"description": "Semantic label for an image."}, "craneFly3SemanticLabel": "Machu Picchu citadel", "@craneFly3SemanticLabel": {"description": "Semantic label for an image."}, "craneFly4SemanticLabel": "Overwater bungalows", "@craneFly4SemanticLabel": {"description": "Semantic label for an image."}, "craneFly5SemanticLabel": "Lake-side hotel in front of mountains", "@craneFly5SemanticLabel": {"description": "Semantic label for an image."}, "craneFly6SemanticLabel": "Aerial view of Palacio de Bellas Artes", "@craneFly6SemanticLabel": {"description": "Semantic label for an image."}, "craneFly7SemanticLabel": "Mount Rushmore", "@craneFly7SemanticLabel": {"description": "Semantic label for an image."}, "craneFly8SemanticLabel": "Supertree Grove", "@craneFly8SemanticLabel": {"description": "Semantic label for an image."}, "craneFly9SemanticLabel": "Man leaning on an antique blue car", "@craneFly9SemanticLabel": {"description": "Semantic label for an image."}, "craneFly10SemanticLabel": "Al-Azhar Mosque towers during sunset", "@craneFly10SemanticLabel": {"description": "Semantic label for an image."}, "craneFly11SemanticLabel": "Brick lighthouse at sea", "@craneFly11SemanticLabel": {"description": "Semantic label for an image."}, "craneFly12SemanticLabel": "Pool with palm trees", "@craneFly12SemanticLabel": {"description": "Semantic label for an image."}, "craneFly13SemanticLabel": "Sea-side pool with palm trees", "@craneFly13SemanticLabel": {"description": "Semantic label for an image."}, "craneSleep0SemanticLabel": "Overwater bungalows", "@craneSleep0SemanticLabel": {"description": "Semantic label for an image."}, "craneSleep1SemanticLabel": "Chalet in a snowy landscape with evergreen trees", "@craneSleep1SemanticLabel": {"description": "Semantic label for an image."}, "craneSleep2SemanticLabel": "Machu Picchu citadel", "@craneSleep2SemanticLabel": {"description": "Semantic label for an image."}, "craneSleep3SemanticLabel": "Man leaning on an antique blue car", "@craneSleep3SemanticLabel": {"description": "Semantic label for an image."}, "craneSleep4SemanticLabel": "Lake-side hotel in front of mountains", "@craneSleep4SemanticLabel": {"description": "Semantic label for an image."}, "craneSleep5SemanticLabel": "Tent in a field", "@craneSleep5SemanticLabel": {"description": "Semantic label for an image."}, "craneSleep6SemanticLabel": "Pool with palm trees", "@craneSleep6SemanticLabel": {"description": "Semantic label for an image."}, "craneSleep7SemanticLabel": "Colorful apartments at Riberia Square", "@craneSleep7SemanticLabel": {"description": "Semantic label for an image."}, "craneSleep8SemanticLabel": "Mayan ruins on a cliff above a beach", "@craneSleep8SemanticLabel": {"description": "Semantic label for an image."}, "craneSleep9SemanticLabel": "Brick lighthouse at sea", "@craneSleep9SemanticLabel": {"description": "Semantic label for an image."}, "craneSleep10SemanticLabel": "Al-Azhar Mosque towers during sunset", "@craneSleep10SemanticLabel": {"description": "Semantic label for an image."}, "craneSleep11SemanticLabel": "Taipei 101 skyscraper", "@craneSleep11SemanticLabel": {"description": "Semantic label for an image."}, "craneEat0SemanticLabel": "Pizza in a wood-fired oven", "@craneEat0SemanticLabel": {"description": "Semantic label for an image."}, "craneEat1SemanticLabel": "Empty bar with diner-style stools", "@craneEat1SemanticLabel": {"description": "Semantic label for an image."}, "craneEat2SemanticLabel": "Burger", "@craneEat2SemanticLabel": {"description": "Semantic label for an image."}, "craneEat3SemanticLabel": "Korean taco", "@craneEat3SemanticLabel": {"description": "Semantic label for an image."}, "craneEat4SemanticLabel": "Chocolate dessert", "@craneEat4SemanticLabel": {"description": "Semantic label for an image."}, "craneEat5SemanticLabel": "Artsy restaurant seating area", "@craneEat5SemanticLabel": {"description": "Semantic label for an image."}, "craneEat6SemanticLabel": "Shrimp dish", "@craneEat6SemanticLabel": {"description": "Semantic label for an image."}, "craneEat7SemanticLabel": "Bakery entrance", "@craneEat7SemanticLabel": {"description": "Semantic label for an image."}, "craneEat8SemanticLabel": "Plate of crawfish", "@craneEat8SemanticLabel": {"description": "Semantic label for an image."}, "craneEat9SemanticLabel": "Cafe counter with pastries", "@craneEat9SemanticLabel": {"description": "Semantic label for an image."}, "craneEat10SemanticLabel": "Woman holding huge pastrami sandwich", "@craneEat10SemanticLabel": {"description": "Semantic label for an image."}, "fortnightlyMenuFrontPage": "Front Page", "@fortnightlyMenuFrontPage": {"description": "Menu item for the front page of the news app."}, "fortnightlyMenuWorld": "World", "@fortnightlyMenuWorld": {"description": "Menu item for the world news section of the news app."}, "fortnightlyMenuUS": "US", "@fortnightlyMenuUS": {"description": "Menu item for the United States news section of the news app."}, "fortnightlyMenuPolitics": "Politics", "@fortnightlyMenuPolitics": {"description": "Menu item for the political news section of the news app."}, "fortnightlyMenuBusiness": "Business", "@fortnightlyMenuBusiness": {"description": "Menu item for the business news section of the news app."}, "fortnightlyMenuTech": "Tech", "@fortnightlyMenuTech": {"description": "Menu item for the tech news section of the news app."}, "fortnightlyMenuScience": "Science", "@fortnightlyMenuScience": {"description": "Menu item for the science news section of the news app."}, "fortnightlyMenuSports": "Sports", "@fortnightlyMenuSports": {"description": "Menu item for the sports news section of the news app."}, "fortnightlyMenuTravel": "Travel", "@fortnightlyMenuTravel": {"description": "Menu item for the travel news section of the news app."}, "fortnightlyMenuCulture": "Culture", "@fortnightlyMenuCulture": {"description": "Menu item for the culture news section of the news app."}, "fortnightlyTrendingTechDesign": "TechDesign", "@fortnightlyTrendingTechDesign": {"description": "Hashtag for the tech design trending topic of the news app."}, "fortnightlyTrendingReform": "Reform", "@fortnightlyTrendingReform": {"description": "Hashtag for the reform trending topic of the news app."}, "fortnightlyTrendingHealthcareRevolution": "HealthcareRevolution", "@fortnightlyTrendingHealthcareRevolution": {"description": "Hashtag for the healthcare revolution trending topic of the news app."}, "fortnightlyTrendingGreenArmy": "GreenArmy", "@fortnightlyTrendingGreenArmy": {"description": "Hashtag for the green army trending topic of the news app."}, "fortnightlyTrendingStocks": "Stocks", "@fortnightlyTrendingStocks": {"description": "Hashtag for the stocks trending topic of the news app."}, "fortnightlyLatestUpdates": "Latest Updates", "@fortnightlyLatestUpdates": {"description": "Title for news section regarding the latest updates."}, "fortnightlyHeadlineHealthcare": "The Quiet, Yet Powerful Healthcare Revolution", "@fortnightlyHeadlineHealthcare": {"description": "Headline for a news article about healthcare."}, "fortnightlyHeadlineWar": "Divided American Lives During War", "@fortnightlyHeadlineWar": {"description": "Headline for a news article about war."}, "fortnightlyHeadlineGasoline": "The Future of Gasoline", "@fortnightlyHeadlineGasoline": {"description": "Headline for a news article about gasoline."}, "fortnightlyHeadlineArmy": "Reforming The Green Army From Within", "@fortnightlyHeadlineArmy": {"description": "Headline for a news article about the green army."}, "fortnightlyHeadlineStocks": "As Stocks Stagnate, Many Look To Currency", "@fortnightlyHeadlineStocks": {"description": "Headline for a news article about stocks."}, "fortnightlyHeadlineFabrics": "Designers Use Tech To Make Futuristic Fabrics", "@fortnightlyHeadlineFabrics": {"description": "Headline for a news article about fabric."}, "fortnightlyHeadlineFeminists": "Feminists Take On Partisanship", "@fortnightlyHeadlineFeminists": {"description": "Headline for a news article about feminists and partisanship."}, "fortnightlyHeadlineBees": "Farmland Bees In Short Supply", "@fortnightlyHeadlineBees": {"description": "Headline for a news article about bees."}, "replyInboxLabel": "Inbox", "@replyInboxLabel": {"description": "Text label for Inbox destination."}, "replyStarredLabel": "Starred", "@replyStarredLabel": {"description": "Text label for Starred destination."}, "replySentLabel": "<PERSON><PERSON>", "@replySentLabel": {"description": "Text label for Sent destination."}, "replyTrashLabel": "Trash", "@replyTrashLabel": {"description": "Text label for Trash destination."}, "replySpamLabel": "Spam", "@replySpamLabel": {"description": "Text label for Spam destination."}, "replyDraftsLabel": "Drafts", "@replyDraftsLabel": {"description": "Text label for Drafts destination."}, "demoTwoPaneFoldableLabel": "Foldable", "@demoTwoPaneFoldableLabel": {"description": "Option title for TwoPane demo on foldable devices."}, "demoTwoPaneFoldableDescription": "This is how TwoPane behaves on a foldable device.", "@demoTwoPaneFoldableDescription": {"description": "Description for the foldable option configuration on the TwoPane demo."}, "demoTwoPaneSmallScreenLabel": "Small Screen", "@demoTwoPaneSmallScreenLabel": {"description": "Option title for TwoPane demo in small screen mode. Counterpart of the foldable option."}, "demoTwoPaneSmallScreenDescription": "This is how TwoPane behaves on a small screen device.", "@demoTwoPaneSmallScreenDescription": {"description": "Description for the small screen option configuration on the TwoPane demo."}, "demoTwoPaneTabletLabel": "Tablet / Desktop", "@demoTwoPaneTabletLabel": {"description": "Option title for TwoPane demo in tablet or desktop mode."}, "demoTwoPaneTabletDescription": "This is how TwoPane behaves on a larger screen like a tablet or desktop.", "@demoTwoPaneTabletDescription": {"description": "Description for the tablet / desktop option configuration on the TwoPane demo."}, "demoTwoPaneTitle": "TwoPane", "@demoTwoPaneTitle": {"description": "Title for the TwoPane widget demo."}, "demoTwoPaneSubtitle": "Responsive layouts on foldable, large, and small screens", "@demoTwoPaneSubtitle": {"description": "Subtitle for the TwoPane widget demo."}, "splashSelectDemo": "Select a demo", "@splashSelectDemo": {"description": "Tip for user, visible on the right side of the splash screen when Gallery runs on a foldable device."}, "demoTwoPaneList": "List", "@demoTwoPaneList": {"description": "Title of one of the panes in the TwoPane demo. It sits on top of a list of items."}, "demoTwoPaneDetails": "Details", "@demoTwoPaneDetails": {"description": "Title of one of the panes in the TwoPane demo, which shows details of the currently selected item."}, "demoTwoPaneSelectItem": "Select an item", "@demoTwoPaneSelectItem": {"description": "Tip for user, visible on the right side of the TwoPane widget demo in the foldable configuration."}, "demoTwoPaneItem": "Item {value}", "@demoTwoPaneItem": {"description": "Generic item placeholder visible in the TwoPane widget demo.", "placeholders": {"value": {"example": "1"}}}, "demoTwoPaneItemDetails": "Item {value} details", "@demoTwoPaneItemDetails": {"description": "Generic item description or details visible in the TwoPane widget demo.", "placeholders": {"value": {"example": "1"}}}}