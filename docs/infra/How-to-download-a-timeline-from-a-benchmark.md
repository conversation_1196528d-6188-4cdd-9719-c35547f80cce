1. Find the benchmark that you're interested in on the [dashboard](https://flutter-dashboard.appspot.com/#/build) by clicking on the gear in the upper-right corner, and searching for it by "Task Name".

<img width="459" alt="Screenshot 2023-06-21 at 6 56 08 PM" src="https://github.com/flutter/flutter/assets/6343103/3e911534-7d2f-4668-8b63-2a23e8fe82ab">

2. Click on the column header icon.

<img width="600" alt="Screenshot 2023-06-21 at 6 59 36 PM" src="https://github.com/flutter/flutter/assets/6343103/a0371897-46e7-49ad-a732-66f0007044aa">


3. Click on the build link corresponding to the commit that you're interested in.
4. In the build page, expand the "log links" step.
5. Download the "timeline.json" file.
<img width="600" alt="Screenshot 2023-06-21 at 4 29 17 PM" src="https://github.com/flutter/flutter/assets/6343103/96265b6c-c6e5-413c-909d-580dbf208c29">
