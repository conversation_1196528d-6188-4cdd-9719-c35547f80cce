# Example of calling platform services from Flutter

This project demonstrates how to connect a Flutter app to platform-specific
services on iOS using Swift. The equivalent version of this project in
Objective C is found in examples/platform_channel.

You can read more about
[accessing platform and third-party services in Flutter](https://flutter.dev/to/platform-channels/).

## iOS

You can use the commands `flutter build` and `flutter run` from the app's root
directory to build/run the app or you can open `ios/Runner.xcworkspace` in Xcode
and build/run the project as usual.

## Android

We refer to the platform_channel project.
