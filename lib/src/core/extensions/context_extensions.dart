import 'package:flutter/material.dart';
import 'package:opti4t_tasks/generated/l10n.dart';

// extension ThemeExtensions on BuildContext {
//   ThemeData get appTheme => Theme.of(this);
//
//   //? Get text style shortcuts
//   TextStyle get headLine => headLineStyle(this);
//
//   TextStyle get whiteHeadLine => headLineStyle(this, isWhiteText: true);
//
//   TextStyle get subHeadLine => subHeadLineStyle(this);
//
//   TextStyle get whiteSubHeadLine => subHeadLineStyle(this, isWhiteText: true);
//
//   TextStyle get authTitle => titleStyle(this).copyWith(
//         fontWeight: FontWeight.w500,
//       );
//
//   TextStyle get title => titleStyle(this);
//
//   TextStyle get whiteTitle => titleStyle(this, isWhiteText: true);
//
//   TextStyle get subTitle => subTitleStyle(this);
//
//   TextStyle get whiteSubTitle => subTitleStyle(this, isWhiteText: true);
//
//   TextStyle get labelLarge => labelLargeStyle(this);
//
//   TextStyle get greyLabelLarge => labelLargeStyle(this)
//       .copyWith(color: const Color(0xff787878), fontSize: 16);
//
//   TextStyle get appBarTitle => labelLargeStyle(this).copyWith(
//       color: ColorManager.primaryColor,
//       letterSpacing: isEng ? 2 : 0,
//       fontWeight: FontWeight.bold);
//
//   TextStyle get whiteLabelLarge => labelLargeStyle(this, isWhiteText: true);
//
//   TextStyle get labelMedium => labelMediumStyle(this);
//
//   TextStyle get whiteLabelMedium => labelMediumStyle(this, isWhiteText: true);
//
//   TextStyle get greyLabelMedium =>
//       labelMediumStyle(this).copyWith(color: const Color(0xffbfc6ce));
//
//   TextStyle get orderGreyLabelMedium => labelMediumStyle(this)
//       .copyWith(color: ColorManager.darkGrey.withOpacity(0.9));
//
//   TextStyle get labelSmall => labelSmallStyle(this);
//
//   TextStyle get whitelabelSmall => labelSmallStyle(this, isWhiteText: true);
//
//   TextStyle get body => bodyStyle(this);
//
//   TextStyle get whiteBody => bodyStyle(this, isWhiteText: true);
//
//   TextStyle get hint => hintStyle(this);
//
//   TextStyle get whiteHint => hintStyle(this, isWhiteText: true);
//   TextStyle get smallHint =>
//       hintStyle(this, isWhiteText: true).copyWith(fontSize: 10);
// }

extension Localization on BuildContext {
  //? Localization shortcuts
  S get tr => S.of(this);
}

extension AppSettings on BuildContext {
  bool get isAppEnglish {
    final locale = Localizations.localeOf(this);
    return locale.languageCode == 'en';
  }

  String langText(String langCode) {
    if (langCode == 'en') return 'English';
    return 'العربية';
  }

  String themeText(int index) {
    switch (index) {
      // case 0:
      //   return tr.system;
      // case 1:
      //   return tr.light;
      // case 2:
      //   return tr.dark;
      default:
        return '';
    }
  }
}
