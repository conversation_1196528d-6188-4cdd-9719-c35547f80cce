import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/cupertino.dart';
import 'package:opti4t_tasks/src/core/consts/app_constants.dart';
import 'package:xr_helper/xr_helper.dart';

// ValueNotifier<bool> isEgApp = ValueNotifier(false);
ValueNotifier<bool> isEgApp = ValueNotifier(GetStorageService.getLocalData(key: LocalKeys.isEgApp) ?? true);

class FirestoreService {
  // static final _db = FirebaseFirestore.instance;

  static FirebaseApp currentFirebaseApp =
      Firebase.app(isEgApp.value ? 'EG' : 'KSA');

  static FirebaseFirestore firestore =
      FirebaseFirestore.instanceFor(app: currentFirebaseApp);

  static final _db = FirebaseFirestore.instanceFor(app: currentFirebaseApp);

  // * Get All Data
  static Future<List<T>> getData<T>({
    required String collection,
    required T Function(Map<String, dynamic> data) builder,
    bool withFilter = false,
  }) async {
    QuerySnapshot<Map<String, dynamic>> snapshot;

    if (withFilter) {
      if (AppConsts.isKSA) {
        snapshot = await _db.collection(collection).get();
      } else {
        snapshot = await _db
            .collection(collection)
            .where('isAdmin', isEqualTo: false)
            .get();
      }
    } else {
      snapshot = await _db.collection(collection).get();
    }

    final data = snapshot.docs.map((doc) => builder(doc.data())).toList();
    return data;
  }

// * Get All Data By builder have id
  static Future<List<T>> getDataWithId<T>({
    required String collection,
    required T Function(String id, Map<String, dynamic> data) builder,
  }) async {
    final snapshot = await _db.collection(collection).get();
    final data =
        snapshot.docs.map((doc) => builder(doc.id, doc.data())).toList();
    return data;
  }

  // * Get All Data By Collection Group
  static Future<List<T>> getDataByCollectionGroup<T>({
    required String collection,
    required T Function(Map<String, dynamic> data) builder,
  }) async {
    final snapshot = await _db.collectionGroup(collection).get();
    final data = snapshot.docs.map((doc) => builder(doc.data())).toList();
    return data;
  }

  // * Get Data By SubCollection
  static Future<List<T>> getDataBySubCollection<T>({
    required String collection,
    required String subCollection,
    required String collectionId,
    required T Function(Map<String, dynamic> data) builder,
  }) async {
    final snapshot = await _db
        .collection(collection)
        .doc(collectionId)
        .collection(subCollection)
        .get();
    final data = snapshot.docs.map((doc) => builder(doc.data())).toList();
    return data;
  }

  // * Get Data By SubCollection SubCollection
  static Future<List<T>> getDataBySubCollectionSubCollection<T>({
    required String collection,
    required String subCollection,
    required String subCollectionSubCollection,
    required String id,
    required String subId,
    required T Function(Map<String, dynamic> data) builder,
  }) async {
    final snapshot = await _db
        .collection(collection)
        .doc(id)
        .collection(subCollection)
        .doc(subId)
        .collection(subCollectionSubCollection)
        .get();
    final data = snapshot.docs.map((doc) => builder(doc.data())).toList();
    return data;
  }

  // * Get Data By Id
  static Future<T> getDataById<T>({
    required String collection,
    required String id,
    required T Function(Map<String, dynamic> data) builder,
  }) async {
    final snapshot = await _db.collection(collection).doc(id).get();
    final data = builder(snapshot.data()!);
    return data;
  }

  // * Get Data By Field
  static Future<List<T>> getDataByField<T>({
    required String collection,
    required String field,
    required String value,
    required T Function(String id, Map<String, dynamic> data) builder,
  }) async {
    final snapshot =
        await _db.collection(collection).where(field, isEqualTo: value).get();
    final data =
        snapshot.docs.map((doc) => builder(doc.id, doc.data())).toList();
    return data;
  }

  // * Get Data By Field
  static Future<List<T>> getDataByFieldAndID<T>({
    required String collection,
    required String field,
    required String value,
    required T Function(String id, Map<String, dynamic> data) builder,
  }) async {
    final snapshot =
        await _db.collection(collection).where(field, isEqualTo: value).get();
    final data =
        snapshot.docs.map((doc) => builder(doc.id, doc.data())).toList();
    return data;
  }

  // * Get Data By 2 Fields
  static Future<List<T>> getDataBy2Fields<T>({
    required String collection,
    required String field1,
    required String value1,
    required String field2,
    required String? value2,
    required T Function(String id, Map<String, dynamic> data) builder,
  }) async {
    Log.i('Collection: $collection - Field1: $field1 - Value1: $value1 - '
        'Field2: $field2 - Value2: $value2');
    final snapshot = await _db
        .collection(collection)
        .where(field1, isEqualTo: value1)
        .where(field2, isEqualTo: value2)
        .orderBy('createdAt', descending: false)
        .get();

    final data =
        snapshot.docs.map((doc) => builder(doc.id, doc.data())).toList();
    return data;
  }

  //        .where(field2, isGreaterThanOrEqualTo: value2)
  //         .where(field3, isLessThanOrEqualTo: value3)

//getDataBy2FieldsByDateRange
  static Future<List<T>> getDataBy2FieldsByDateRange<T>({
    required String collection,
    required String field1,
    required String value1,
    required String field2,
    required String value2,
    required T Function(String id, Map<String, dynamic> data) builder,
  }) async {
    Log.i('Collection: $collection - Field1: $field1 - Value1: $value1 - '
        'Field2: $field2 - Value2: $value2');
    final snapshot = await _db
        .collection(collection)
        .where(field1, isGreaterThanOrEqualTo: value1)
        .where(field2, isLessThanOrEqualTo: value2)
        .orderBy('createdAt', descending: false)
        .get();

    final data =
        snapshot.docs.map((doc) => builder(doc.id, doc.data())).toList();
    return data;
  }

  //getStreamData
  static Stream<List<T>> getStreamData<T>(
      {required String collection,
      required T Function(String id, Map<String, dynamic> data) builder,
      bool withDateSort = false,
      String? uid,
      bool isArray = false}) {
    Log.w('Collection: $collection');
    try {
      if (withDateSort) {
        if (isArray) {
          log('asfasasfsaf');
          return _db
              .collection(collection)
              .orderBy('createdAt', descending: true)
              .where('selected_employees', arrayContains: uid)
              .snapshots()
              .map((snapshot) => snapshot.docs
                  .map((doc) => builder(doc.id, doc.data()))
                  .toList());
        }

        return _db
            .collection(collection)
            .orderBy('createdAt', descending: true)
            .snapshots()
            .map((snapshot) => snapshot.docs
                .map((doc) => builder(doc.id, doc.data()))
                .toList());
      }

      if (uid != null) {
        return _db
            .collection(collection)
            .where('uid', isEqualTo: uid)
            .snapshots()
            .map((snapshot) => snapshot.docs
                .map((doc) => builder(doc.id, doc.data()))
                .toList());
      }

      return _db.collection(collection).snapshots().map((snapshot) =>
          snapshot.docs.map((doc) => builder(doc.id, doc.data())).toList());
    } catch (e) {
      Log.e('Error: $e');
      rethrow;
    }
  }

  //? getStreamDataByField
  static Stream<List<T>> getStreamDataByField<T>({
    required String collection,
    required String field,
    required String value,
    required T Function(String id, Map<String, dynamic> data) builder,
  }) {
    Log.i('Collection: $collection - Field: $field - Value: $value');
    return _db
        .collection(collection)
        .where(field, isEqualTo: value)
        .orderBy('createdAt', descending: false)
        .snapshots()
        .map((snapshot) =>
            snapshot.docs.map((doc) => builder(doc.id, doc.data())).toList());
  }

  //? getStreamDataBy2Fields
  static Stream<List<T>> getStreamDataBy2Fields<T>({
    required String collection,
    required String field1,
    required String value1,
    required String field2,
    required value2,
    required T Function(String id, Map<String, dynamic> data) builder,
  }) {
    Log.i('Collection: $collection - Field1: $field1 - Value1: $value1 - '
        'Field2: $field2 - Value2: $value2');
    return _db
        .collection(collection)
        .where(field1, isEqualTo: value1)
        .where(field2, isEqualTo: value2)
        .orderBy('createdAt', descending: false)
        .snapshots()
        .map((snapshot) =>
            snapshot.docs.map((doc) => builder(doc.id, doc.data())).toList());
  }

  // * Get Data By 3 Fields
  static Future<List<T>> getDataBy3Fields<T>({
    required String collection,
    required String field1,
    required String value1,
    required String field2,
    required String value2,
    required String field3,
    required String? value3,
    required T Function(String id, Map<String, dynamic> data) builder,
  }) async {
    Log.i('Collection: $collection - Field1: $field1 - Value1: $value1 - '
        'Field2: $field2 - Value2: $value2 - Field3: $field3 - Value3: $value3');

    final snapshot = await _db
        .collection(collection)
        .where(field1, isEqualTo: value1)
        .where(field2, isGreaterThanOrEqualTo: value2)
        .where(field3, isLessThanOrEqualTo: value3)
        // .orderBy('createdAt', descending: false)
        .get();

    final data =
        snapshot.docs.map((doc) => builder(doc.id, doc.data())).toList();
    return data;
  }

  // * Create
  static Future<void> createData({
    required String collection,
    required Map<String, dynamic> data,
    String? id,
  }) async {
    Log.w('Collection: $collection\nId: $id\nData: $data');
    try {
      if (id != null) {
        await _db.collection(collection).doc(id).set(data);
      } else {
        await _db.collection(collection).add(data);
      }
    } catch (e) {
      Log.e('Error: $e');
    }
  }

  // * Update
  static Future<void> updateData({
    required String collection,
    required String id,
    required Map<String, dynamic> data,
  }) async {
    await _db.collection(collection).doc(id).update(data);
  }

  // * Update SubCollection
  static Future<void> updateSubCollection({
    required String collection,
    required String collectionId,
    required String subCollection,
    required String subCollectionId,
    required Map<String, dynamic> data,
  }) async {
    Log.f(
        'Collection: $collection - CollectionId: $collectionId - SubCollection: $subCollection - SubCollectionId: $subCollectionId - Data: $data');
    await _db
        .collection(collection)
        .doc(collectionId)
        .collection(subCollection)
        .doc(subCollectionId)
        .update(data);
  }

  // * Delete
  static Future<void> deleteData({
    required String collection,
    required String id,
  }) async {
    await _db.collection(collection).doc(id).delete();
  }

  // * Delete By Field
  static Future<void> deleteDataByField({
    required String collection,
    required String field,
    required String value,
  }) async {
    final snapshot =
        await _db.collection(collection).where(field, isEqualTo: value).get();
    for (final doc in snapshot.docs) {
      await doc.reference.delete();
    }
  }
}
