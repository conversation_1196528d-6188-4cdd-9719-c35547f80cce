import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/extensions/riverpod_extensions.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/shared_widgets.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/clients/models/client_model.dart';
import 'package:opti4t_tasks/src/screens/clients/providers/clients_providers.dart';

class ClientsDropDown extends HookConsumerWidget {
  final String? label;
  final ValueNotifier<ClientRequestModel?>? selectedClient;
  final ValueNotifier<List<ClientRequestModel?>>? allClients;
  final String? clientId;
  final bool withAllClients;
  final bool isRequired;
  final Function(List<ClientRequestModel> requests)? additionalFunction;
  final bool isProjects;

  const ClientsDropDown({
    super.key,
    this.label,
    required this.selectedClient,
    this.allClients,
    this.withAllClients = false,
    this.clientId,
    this.isRequired = true,
    this.isProjects = false,
    this.additionalFunction,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final label = this.label ?? context.tr.client;

    final getClientsController =
        ref.watch(getClientsFutureProvider((context, isProjects)));

    return getClientsController.get(
      data: (clients) {
        List<ClientRequestModel?> filteredClients = clients
            .where((element) =>
                element.isParentClient == true &&
                (UserModelHelper.isManager()
                    ? true
                    : element.selectedEmployees
                        .contains(UserModelHelper.localUID())))
            .toList();

        return HookBuilder(builder: (context) {
          useEffect(() {
            allClients?.value = filteredClients;

            if (clientId != null && filteredClients.isNotEmpty) {
              final client = filteredClients
                  .firstWhereOrNull((element) => element?.id == clientId);

              if (client != null) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  selectedClient?.value = client;
                });
              }
            }

            return () {};
          }, [clientId]);

          return BaseSearchDropDown(
            data: filteredClients,
            itemModelAsName: (client) {
              final user = (client as ClientRequestModel);

              return user.name;
            },
            label: label,
            onChanged: (value) {
              selectedClient?.value = value;

              if (additionalFunction != null) {
                final filteredRequestsByClient = clients
                    .where((element) => element.parentId == value?.id)
                    .toList();

                additionalFunction!(filteredRequestsByClient);
              }
            },
            selectedValue: selectedClient?.value,
            isRequired: isRequired,
          );
        });
      },
    );
  }
}

// useEffect(() {
// if (clientId != null && clients.isNotEmpty) {
// final client =
// clients.firstWhere((element) => element?.id == clientId);
//
// if (client != null) {
// WidgetsBinding.instance.addPostFrameCallback((_) {
//
// selectedValue?.value = client;
// });
// }
// }
//
// return () {};
// }, [clientId]);
