import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/consts/app_constants.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/extensions/riverpod_extensions.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/shared_widgets.dart';
import 'package:opti4t_tasks/src/screens/auth/controllers/auth_controller.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';

class EmployeesDropDown extends HookConsumerWidget {
  final String? label;
  final ValueNotifier<UserModel?>? selectedEmployee;
  final String? employeeId;
  final bool withAllEmployees;
  final bool isRequired;

  const EmployeesDropDown({
    super.key,
    this.label,
    required this.selectedEmployee,
    this.withAllEmployees = false,
    this.employeeId,
    this.isRequired = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final label = this.label ?? context.tr.employee;

    final getEmployeesController =
        ref.watch(getUsersControllerProvider((context, withAllEmployees)));

    return getEmployeesController.get(
      data: (allEmployees) {
        List<UserModel?> employees = [];

        if (!withAllEmployees) {
          employees = allEmployees
              .where((element) => element?.uid != AppConsts.allEmployees)
              .toList();
        } else {
          employees = allEmployees;
        }

        return HookBuilder(builder: (context) {
          useEffect(() {
            if (employeeId != null && employees.isNotEmpty) {
              final employee = employees
                  .firstWhereOrNull((element) => element?.uid == employeeId);

              if (employee != null) {
                WidgetsBinding.instance!.addPostFrameCallback((_) {
                  selectedEmployee?.value = employee;
                });
              }
            }

            return () {};
          }, [employeeId]);

          return BaseSearchDropDown(
            data: employees,
            itemModelAsName: (employee) {
              final user = (employee as UserModel);

              return '${user.name}\n${user.email}';
            },
            label: label,
            onChanged: (value) => selectedEmployee?.value = value,
            selectedValue: selectedEmployee?.value,
            isRequired: isRequired,
          );
        });
      },
    );
  }
}

// useEffect(() {
// if (employeeId != null && employees.isNotEmpty) {
// final employee =
// employees.firstWhere((element) => element?.id == employeeId);
//
// if (employee != null) {
// WidgetsBinding.instance.addPostFrameCallback((_) {
//
// selectedValue?.value = employee;
// });
// }
// }
//
// return () {};
// }, [employeeId]);
