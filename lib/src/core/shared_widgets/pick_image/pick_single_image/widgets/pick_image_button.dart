part of shared_widgets;

class _PickImageButton extends ConsumerWidget {
  const _PickImageButton();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(vertical: AppSpaces.mediumPadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppRadius.smallRadius),
          color: ColorManager.primaryColor.withOpacity(0.2),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.add_photo_alternate_outlined,
              color: ColorManager.primaryColor,
              size: 25,
            ),
            context.mediumGap,
            Text(
              context.tr.uploadAttachment,
              style: const TextStyle(
                color: ColorManager.primaryColor,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ));
  }
}
