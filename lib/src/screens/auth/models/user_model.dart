import 'package:equatable/equatable.dart';
import 'package:opti4t_tasks/src/core/data/remote/api_consts.dart';

class UserModel extends Equatable {
  final String? uid;
  final String name;
  final String? image;
  final String? fcmToken;
  final bool isAdmin;
  final bool canViewAllTasks;

  //? For Login
  final String email;
  final String password;

  final bool isActive;

  const UserModel({
    this.uid,
    this.name = '',
    this.image,
    this.fcmToken,
    this.isAdmin = false,
    this.email = '',
    this.password = '',
    this.canViewAllTasks = false,
    this.isActive = true,
  });

  // * For Login ================================
  factory UserModel.fromJson(Map<String, dynamic> json, {String? uid}) {
    return UserModel(
      uid: uid ?? json[ApiConsts.uid] ?? '',
      name: json[ApiConsts.name] ?? '',
      image: json[ApiConsts.image] ?? '',
      fcmToken: json[ApiConsts.fcmToken],
      email: json[ApiConsts.email] ?? '',
      password: json[ApiConsts.password],
      isAdmin: json[ApiConsts.isAdmin] ?? false,
      canViewAllTasks: json[ApiConsts.canViewAllTasks] ?? false,
      isActive: json[ApiConsts.isActive] ?? true,
    );
  }

  // * To Json ================================
  Map<String, dynamic> toJson() {
    return {
      ApiConsts.uid: uid,
      ApiConsts.name: name,
      if (fcmToken != null && fcmToken!.isNotEmpty)
        ApiConsts.fcmToken: fcmToken,
      ApiConsts.email: email,
      ApiConsts.password: password,
      ApiConsts.isAdmin: isAdmin,
      ApiConsts.canViewAllTasks: canViewAllTasks,
      ApiConsts.isActive: isActive,
    };
  }

  @override
  List<Object?> get props => [
        uid,
        name,
        fcmToken,
        email,
        password,
        isAdmin,
        canViewAllTasks,
        isActive,
      ];
}
