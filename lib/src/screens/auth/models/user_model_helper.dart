//  //? Copy With
//   UserModel copyWith({
//     String? uid,
//     String? name,
//     String? image,
//     String? phone,
//     String? fcmToken,
//     String? email,
//     String? password,
//     bool? isAdmin,
//   }) {
//     return UserModel(
//       uid: uid ?? this.uid,
//       name: name ?? this.name,
//       image: image ?? this.image,
//       fcmToken: fcmToken ?? this.fcmToken,
//       email: email ?? this.email,
//       password: password ?? this.password,
//       isAdmin: isAdmin ?? this.isAdmin,
//     );
//   }

//  //! Empty User Model
//   static const UserModel empty = UserModel();
//
//   //? Get uuid
//   static String localUID() {
//     final localUser = signedUser();
//
//     return localUser.uid ?? '';
//   }
//
//   //? is Manager
//   static bool isManager() {
//     final localUser = signedUser();
//
//     return localUser.isAdmin;
//   }
//
//   //? Get Local User Data
//   static UserModel signedUser() {
//     final userData = GetStorageService.getLocalData(key: LocalKeys.user);
//
//     if (userData == null) {
//       return empty;
//     }
//
//     return UserModel.fromJson(userData);
//   }

import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:xr_helper/xr_helper.dart';

extension UserModelHelper on UserModel {
  //? Empty User Model
  static const UserModel empty = UserModel();

  //? Get uuid
  static String localUID() {
    final localUser = signedUser();

    return localUser.uid ?? '';
  }

  //? is Manager
  static bool isManager() {
    final localUser = signedUser();

    return localUser.isAdmin;
  }

  static bool canViewAllTasks() {
    final localUser = signedUser();

    return localUser.canViewAllTasks;
  }

  static bool isAdmin() {
    return isManager() || canViewAllTasks();
  }

  static bool isEmployee() {
    return !isAdmin();
  }

  //? Get Local User Data
  static UserModel signedUser() {
    final userData = GetStorageService.getLocalData(key: LocalKeys.user);

    if (userData == null) {
      return empty;
    }

    return UserModel.fromJson(userData);
  }

  //? Copy With
  UserModel copyWith({
    String? uid,
    String? name,
    String? image,
    String? phone,
    String? fcmToken,
    String? email,
    String? password,
    bool? isAdmin,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      name: name ?? this.name,
      image: image ?? this.image,
      fcmToken: fcmToken ?? this.fcmToken,
      email: email ?? this.email,
      password: password ?? this.password,
      isAdmin: isAdmin ?? this.isAdmin,
    );
  }

//    final daySelectedBeforeToday = selectedDayValue.value
//         .isBefore(DateTime.now().subtract(const Duration(days: 1)));
//
//     final selectedEmployeeIsNotNullIfManager =
//         selectedEmployee?.value != null && UserModelHelper.isManager();
//
//     final isEmployeeAbsent = daySelectedBeforeToday &&
//         tasks.isEmpty &&
//         selectedEmployeeIsNotNullIfManager &&
//         unClosedTasks.isEmpty;
}
