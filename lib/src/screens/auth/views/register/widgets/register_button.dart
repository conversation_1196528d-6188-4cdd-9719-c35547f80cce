part of register;

class _RegisterButton extends ConsumerWidget {
  final GlobalKey<FormState> formKey;
  final TextEditingController nameController;
  final TextEditingController emailController;
  final TextEditingController passwordController;

  const _RegisterButton(
      {required this.nameController,
      required this.form<PERSON><PERSON>,
      required this.emailController,
      required this.passwordController});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authControllerState =
        ref.watch(authControllerNotifierProvider(context));
    final authController = ref.read(authControllerNotifierProvider(context));

    return SizedBox(
      height: 50,
      width: double.infinity,
      child: But<PERSON>(
        loadingWidget: const LoadingWidget(),
        isLoading: authControllerState.isLoading,
        label: context.tr.register,
        color: ColorManager.primaryColor,
        textColor: Colors.white,
        onPressed: () async {
          if (!formKey.currentState!.validate()) return;

          final name = nameController.text;
          final email = emailController.text;
          final password = passwordController.text;

          final loggedIn = await authController.register(
            name: name,
            email: email,
            password: password,
          );

          if (context.mounted && loggedIn) {
            // Restart.restartApp();
            context.toReplacement(const HomeScreen());
          }
        },
      ),
    );
  }
}
