import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti4t_tasks/src/core/consts/firestore_strings.dart';
import 'package:opti4t_tasks/src/core/services/firebase/firestore_service.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:xr_helper/xr_helper.dart';

final authRepoProvider = Provider<AuthRepository>((ref) {
  return AuthRepository();
});

class AuthRepository with BaseRepository {
  Future<User?> signInWithEmailAndPassword({required UserModel user}) async {
    return await baseFunction(() async {
      final FirebaseAuth firebaseAuth =
          FirebaseAuth.instanceFor(app: FirestoreService.currentFirebaseApp);

      final result = await firebaseAuth.signInWithEmailAndPassword(
        email: user.email,
        password: user.password,
      );

      Log.f('User UID: ${result.user?.uid}');

      //! Get User Data From Firestore
      final userFromFirestore =
          await getUserDataFromFirestore(userId: result.user!.uid);

      //! Save User Data To Local
      if (userFromFirestore != null) {
        saveUserData(userFromFirestore.toJson());

        Log.f('User Data: ${userFromFirestore.toJson()}');
      }

      return result.user;
    });
  }

  //? Get User Data From Same Login Id From Firestore
  Future<UserModel?> getUserDataFromFirestore({required String userId}) async {
    try {
      final data = await FirestoreService.getDataByField(
        collection: FirebaseStrings.users,
        field: FirebaseStrings.uid,
        value: userId,
        builder: (_, data) => UserModel.fromJson(data),
      );

      return data.firstOrNull;
    } catch (e) {
      rethrow;
    }
  }

  //? Save to local
  void saveUserData(Map<String, dynamic> userData) {
    GetStorageService.setLocalData(key: LocalKeys.user, value: userData);
  }

  //! Logout
  Future<void> logout() async {
    await baseFunction(
      () async {
        await FirebaseAuth.instance.signOut();

        //! Clear Local Data
        GetStorageService.removeKey(
          key: LocalKeys.user,
        );
      },
    );
  }

  List<UserModel> employees = [];

  //! Get Users
  Future<List<UserModel?>> getUsers() async {
    if (employees.isNotEmpty) return employees;

    return await baseFunction(() async {
      final users = await FirestoreService.getData(
        collection: FirebaseStrings.users,
        withFilter: true,
        builder: (data) => UserModel.fromJson(data),
      );

      employees = users;

      return users;
    });
  }

  //! Get Users Stream
  Stream<List<UserModel>> getUsersStream() {
    return FirestoreService.getStreamData(
      collection: FirebaseStrings.users,
      builder: (id, data) => UserModel.fromJson(data, uid: id),
    );
  }

  //! Get Current User Stream
  Stream<List<UserModel>> getCurrentUserStream() {
    return FirestoreService.getStreamData(
      collection: FirebaseStrings.users,
      builder: (id, data) => UserModel.fromJson(data, uid: id),
      uid: UserModelHelper.localUID(),
    );
  }

  //! Delete User
  Future<void> deleteUser({required UserModel user}) async {
    await baseFunction(
      () async {
        await FirestoreService.deleteData(
          collection: FirebaseStrings.users,
          id: user.uid.toString(),
        );
      },
    );
  }

  //! Add - Update User
  Future<void> addUpdateUser({required UserModel user}) async {
    await baseFunction(
      () async {
        if (user.uid == null) {
          final FirebaseAuth firebaseAuth = FirebaseAuth.instance;

          await firebaseAuth.createUserWithEmailAndPassword(
            email: user.email,
            password: user.password,
          );

          await FirestoreService.createData(
            collection: FirebaseStrings.users,
            id: user.uid,
            data: user.toJson(),
          );
        } else {
          await FirestoreService.updateData(
            collection: FirebaseStrings.users,
            id: user.uid.toString(),
            data: user.toJson(),
          );
        }
      },
    );
  }

  //! Register
  Future<User?> registerWithEmailAndPassword({required UserModel user}) async {
    try {
      final FirebaseAuth firebaseAuth = FirebaseAuth.instance;

      final result = await firebaseAuth.createUserWithEmailAndPassword(
        email: user.email,
        password: user.password,
      );

      Log.f('User UID: ${result.user?.uid}');

      final fcmToken = await NotificationService.getToken();

      final copiedUser =
          user.copyWith(uid: result.user!.uid, fcmToken: fcmToken);

      //! Save User Data To Firestore
      await saveUserDataToFirestore(
        user: copiedUser,
      );

      //! Save User Data To Local
      saveUserData(copiedUser.toJson());

      return result.user;
    } catch (e) {
      rethrow;
    }
  }

  //? Save User Data To Firestore
  Future<void> saveUserDataToFirestore({required UserModel user}) async {
    try {
      await FirestoreService.createData(
        collection: FirebaseStrings.users,
        id: user.uid,
        data: user.toJson(),
      );
    } catch (e) {
      rethrow;
    }
  }
}
