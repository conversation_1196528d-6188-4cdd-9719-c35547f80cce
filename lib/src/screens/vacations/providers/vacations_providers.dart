import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti4t_tasks/src/screens/vacations/controllers/vacations_controller.dart';
import 'package:opti4t_tasks/src/screens/vacations/models/vacation_model.dart';
import 'package:opti4t_tasks/src/screens/vacations/repositories/vacation_repository.dart';

// Provider
final vacationControllerProvider =
    Provider.family<VacationController, BuildContext>(
  (ref, context) {
    final vacationRepo = ref.watch(vacationRepoProvider);
    return VacationController(context, vacationRepo: vacationRepo);
  },
);

// Change Notifier Provider
final vacationControllerNotifierProvider =
    ChangeNotifierProvider.family<VacationController, BuildContext>(
  (ref, context) {
    final vacationRepo = ref.watch(vacationRepoProvider);
    return VacationController(context, vacationRepo: vacationRepo);
  },
);

// Get Vacations Stream Provider
final getVacationsStreamControllerProvider = StreamProvider.family
    .autoDispose<List<VacationModel>, (BuildContext, String?)>(
  (ref, params) {
    final vacationController = ref.watch(vacationControllerProvider(params.$1));
    return vacationController.getVacationsStream(userId: params.$2);
  },
);
