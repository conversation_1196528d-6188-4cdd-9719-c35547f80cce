import 'package:flutter/cupertino.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/services/send_email/send_email_service.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/vacations/models/vacation_model.dart';
import 'package:opti4t_tasks/src/screens/vacations/repositories/vacation_repository.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/consts/app_constants.dart';

// final vacationControllerProvider =
//     Provider.family<VacationController, BuildContext>(
//   (ref, context) {
//     final vacationRepo = ref.watch(vacationRepoProvider);
//
//     return VacationController(
//       context,
//       vacationRepo: vacationRepo,
//     );
//   },
// );

class VacationController extends BaseVM {
  final BuildContext context;
  final VacationRepository vacationRepo;

  VacationController(
    this.context, {
    required this.vacationRepo,
  });

  Future<void> addVacation({
    required VacationModel vacation,
  }) async {
    await baseFunction(
      context,
      () async {
        await vacationRepo.addVacation(vacation: vacation);

        await SendEmailService.sendEmail(
            subject: "طلب اجازة جديد",
            body:
                "طلب اجازة جديد من موظف: ${UserModelHelper.signedUser().name}\nبتاريخ: ${vacation.fromDate.formatDateToString}\nالى: ${vacation.toDate.formatDateToString}\nالسبب: ${vacation.reason}",
            recipient: [AppConsts.managerEmail]);
      },
      additionalFunction: (_) {
        context.back();

        context.showBarMessage(
          context.tr.addedSuccessfully,
        );
      },
    );
  }

  // Future<void> updateVacation({
  //   required VacationModel vacation,
  // }) async {
  //   await baseFunction(
  //     context,
  //     () async {
  //       await vacationRepo.updateVacation(vacation: vacation);
  //     },
  //   );
  // }

  Future<List<VacationModel>> getVacations({
    required DateTime date,
    required DateTime toDate,
    String? userId,
  }) async {
    return await baseFunction(
      context,
      () async {
        return await vacationRepo.getVacations(
            date: date, toDate: toDate, userId: userId);
      },
    );
  }

  Stream<List<VacationModel>> getVacationsStream({
    String? userId,
  }) {
    return vacationRepo.getVacationsStream(userId: userId);
  }

  Future<void> approveVacation({
    required VacationModel vacation,
  }) async {
    await baseFunction(
      context,
      () async {
        final managerReason = vacation.managerReason;

        await vacationRepo.approveVacations(vacation: vacation);

        await SendEmailService.sendEmail(
            subject: "تمت الموافقة على اجازتك",
            body:
                "تمت الموافقة على اجازتك بنجاح الخاصة\nبتاريخ: ${vacation.fromDate.formatDateToString}\nالى: ${vacation.toDate.formatDateToString}\n${managerReason.isNotEmpty ? 'بسبب: $managerReason' : ''}",
            recipient: [vacation.userEmail]);
      },
      additionalFunction: (_) {
        context.back();
        context.showBarMessage(
          context.tr.approvedSuccessfully,
        );
      },
    );
  }

  // ! Reject Vacation
  Future<void> rejectVacation({
    required VacationModel vacation,
  }) async {
    await baseFunction(
      context,
      () async {
        final managerReason = vacation.managerReason;
        await vacationRepo.rejectVacations(vacation: vacation);

        await SendEmailService.sendEmail(
            subject: "تم رفض اجازتك",
            body:
                "تم رفض اجازتك الخاصة\nبتاريخ: ${vacation.fromDate.formatDateToString}\nالى: ${vacation.toDate.formatDateToString}\n${managerReason.isNotEmpty ? 'بسبب: $managerReason' : ''}",
            recipient: [vacation.userEmail]);
      },
      additionalFunction: (_) {
        context.back();
        context.showBarMessage(
          context.tr.rejectedSuccessfully,
          isError: true,
        );
      },
    );
  }

  Future<void> deleteVacation({
    required VacationModel vacation,
  }) async {
    await baseFunction(
      context,
      () async {
        await vacationRepo.deleteVacation(vacation: vacation);
      },
      additionalFunction: (_) {
        context.back();
        context.showBarMessage(
          context.tr.deletedSuccessfully,
          isError: true,
        );
      },
    );
  }
}
