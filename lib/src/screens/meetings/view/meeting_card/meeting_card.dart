import 'package:flutter/material.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/meetings/models/meeting_model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../auth/models/user_model_helper.dart';
import '../widgets/add_meeting_floating_button.dart';
import '../widgets/delete_meeting_button.dart';

class MeetingCard extends StatelessWidget {
  final MeetingModel meeting;

  const MeetingCard({
    super.key,
    required this.meeting,
  });

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: ValueKey(meeting.id),
      confirmDismiss: (direction) async {
        if (UserModelHelper.isManager()) {
          return await showDialog(
              context: context,
              builder: (_) => DeleteMeetingDialog(
                    meeting: meeting,
                  ));
        } else {
          return false;
        }
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: AppSpaces.smallPadding),
        padding: const EdgeInsets.only(bottom: AppSpaces.smallPadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppRadius.mediumRadius),
          color: ColorManager.secondaryColor.withOpacity(.1),
        ),
        child: ListTile(
            title: Text(
              '${context.tr.date}: ${meeting.date.formatDateToString}',
              style: context.whiteLabelMedium,
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (meeting.title.isNotEmpty) ...[
                  context.xSmallGap,
                  Text('${context.tr.title}: ${meeting.title}'),
                ],
                if (meeting.participants.isNotEmpty) ...[
                  context.xSmallGap,
                  Text(
                    '${context.tr.participants}: ${meeting.participants}',
                    style: context.whiteLabelMedium,
                  ),
                ],
                if (meeting.description.isNotEmpty) ...[
                  context.xSmallGap,
                  Text(
                    '${context.tr.description}: ${meeting.description}',
                    style: context.whiteLabelMedium,
                  ),
                ],
              ],
            ),
            trailing: FloatingActionButton.small(
              backgroundColor: ColorManager.successColor,
              onPressed: () => showDialog(
                  context: context,
                  builder: (_) => AddMeetingDialog(
                        meeting: meeting,
                      )),
              child: const Icon(Icons.edit),
            )
            // trailing:
            ),
      ),
    );
  }
}
