import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:xr_helper/xr_helper.dart';

import '../../reports/repositories/report_repository_helper.dart';
import '../models/meeting_model.dart';

Future<pw.Document> generateMeetingsReportPDF({
  required List<MeetingModel> meetings,
  required bool isEnglish,
}) async {
  final arabicFont = pw.Font.ttf(
    await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
  );

  final pdf = pw.Document();

  List<pw.TableRow> generateMeetingRows({
    required List<MeetingModel> meetings,
    required pw.Font arabicFont,
    bool isArabic = false,
  }) {
    return meetings.map((meeting) {
      if (isArabic) {
        return pw.TableRow(
          children: [
            baseText(meeting.description ?? '-', arabicFont: arabicFont),
            baseText(meeting.participants.toString(), arabicFont: arabicFont),
            baseText(meeting.title, arabicFont: arabicFont),
            baseText(meeting.date.formatDateToStringWithTime,
                arabicFont: arabicFont),
          ],
        );
      } else {
        return pw.TableRow(
          children: [
            baseText(meeting.date.formatDateToStringWithTime,
                arabicFont: arabicFont),
            baseText(meeting.title, arabicFont: arabicFont),
            baseText(meeting.participants.toString(), arabicFont: arabicFont),
            baseText(meeting.description ?? '-', arabicFont: arabicFont),
          ],
        );
      }
    }).toList();
  }

  pw.Widget englishTable() {
    List<pw.TableRow> headerRow = [
      pw.TableRow(
        children: [
          baseText('Date', arabicFont: arabicFont, isBold: true),
          baseText('Title', arabicFont: arabicFont, isBold: true),
          baseText('Participants', arabicFont: arabicFont, isBold: true),
          baseText('Description', arabicFont: arabicFont, isBold: true),
        ],
      ),
    ];
    List<pw.TableRow> meetingRows = generateMeetingRows(
      meetings: meetings,
      arabicFont: arabicFont,
    );
    return pw.Table(
      columnWidths: {
        0: const pw.FlexColumnWidth(2),
        1: const pw.FlexColumnWidth(2),
        2: const pw.FlexColumnWidth(2),
        3: const pw.FlexColumnWidth(3),
      },
      border: pw.TableBorder.all(),
      children: headerRow + meetingRows,
    );
  }

  pw.Widget arabicTable() {
    List<pw.TableRow> headerRow = [
      pw.TableRow(
        children: [
          baseText('الوصف', arabicFont: arabicFont, isBold: true),
          baseText('المشاركون', arabicFont: arabicFont, isBold: true),
          baseText('العنوان', arabicFont: arabicFont, isBold: true),
          baseText('التاريخ', arabicFont: arabicFont, isBold: true),
        ],
      ),
    ];
    List<pw.TableRow> meetingRows = generateMeetingRows(
      meetings: meetings,
      arabicFont: arabicFont,
      isArabic: true,
    );
    return pw.Table(
      columnWidths: {
        0: const pw.FlexColumnWidth(3),
        1: const pw.FlexColumnWidth(2),
        2: const pw.FlexColumnWidth(2),
        3: const pw.FlexColumnWidth(2),
      },
      border: pw.TableBorder.all(),
      children: headerRow + meetingRows,
    );
  }

  pdf.addPage(
    pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      build: (pw.Context context) {
        return <pw.Widget>[
          pw.Header(
              level: 0,
              child: pw.Align(
                alignment: pw.Alignment.center,
                child: pw.Text(
                  isEnglish ? 'Meetings Report' : 'تقرير الاجتماعات',
                  textScaleFactor: 2,
                  textAlign: pw.TextAlign.center,
                  textDirection:
                      isEnglish ? pw.TextDirection.ltr : pw.TextDirection.rtl,
                  style: pw.TextStyle(
                    font: arabicFont,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              )),
          isEnglish ? englishTable() : arabicTable(),
        ];
      },
    ),
  );

  return pdf;
}
