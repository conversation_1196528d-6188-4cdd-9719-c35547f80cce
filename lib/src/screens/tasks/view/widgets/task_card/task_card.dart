import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/animated/lottie_icon.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/home/<USER>/widgets/floating_buttons/add_task_floating_button.dart';
import 'package:opti4t_tasks/src/screens/tasks/models/task_model.dart';
import 'package:opti4t_tasks/src/screens/tasks/view/widgets/task_card/widgets/employee_tasks_actions.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/admin_actions/admin_task_actions.dart';

final isExpanded = ValueNotifier(true);

class TaskCard extends ConsumerWidget {
  final TaskModel task;
  final ValueNotifier<DateTime> selectedDayValue;
  final ValueNotifier<UserModel?>? selectedEmployee;
  final List<TaskModel> projectTasks;

  const TaskCard(
      {super.key,
      required this.task,
      required this.selectedDayValue,
      required this.selectedEmployee,
      required this.projectTasks});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final canAddOrEditTasks = selectedEmployee?.value == null ||
        selectedEmployee?.value?.uid == UserModelHelper.signedUser().uid &&
            UserModelHelper.canViewAllTasks();

    // key: ValueKey(task.id),
    //       // confirmDismiss: (direction) async {
    //       //   if (UserModelHelper.isManager()) {
    //       //     return true;
    //       //   } else {
    //       //     return await showDialog(
    //       //       context: context,
    //       //       builder: (_) => DeleteTaskDialog(task: task),
    //       //     );
    //       //   }
    //       // },
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpaces.smallPadding),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppRadius.smallRadius),
        color: ColorManager.secondaryColor.withOpacity(.1),
      ),
      child: ExpansionTile(
        initiallyExpanded: isExpanded.value,
        onExpansionChanged: (value) {
          isExpanded.value = value;
        },
        childrenPadding: const EdgeInsets.only(bottom: AppSpaces.smallPadding),
        tilePadding: const EdgeInsets.symmetric(
            horizontal: AppSpaces.mediumPadding,
            vertical: AppSpaces.xSmallPadding),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.smallRadius),
        ),
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //? Date
                Text(
                  task.createdAt?.toDate().formatDateToStringWithTime ?? '',
                  style: context.whiteHint.copyWith(
                    fontSize: 13,
                  ),
                ),

                context.smallGap,

                SizedBox(
                  width: context.width * 0.5,
                  child: Text(
                    task.project,
                    style: context.whiteTitle.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            if (canAddOrEditTasks && selectedDayValue.value.isToday())
              Row(
                children: [
                  //! Edit
                  const CircleAvatar(
                          backgroundColor: ColorManager.successColor,
                          child: BaseLottieIcon('assets/animated/edit.json',
                              width: 20, height: 20))
                      .onTapWithRipple(
                          () => showDialog(
                                context: context,
                                builder: (_) => AddTaskDialog(
                                  task: task,
                                ),
                              ),
                          radius: 5),

                  context.smallGap,

                  //! Add
                  AddTaskFloatingButton(
                    selectedDate: selectedDayValue,
                    parentTaskId: task.id,
                    selectedEmployee: selectedEmployee,
                  ),
                ],
              )
          ],
        ),
        children: projectTasks.isEmpty
            ? [
                Center(
                  child: Text(context.tr.noTasks,
                      style: context.whiteTitle.copyWith(
                        fontWeight: FontWeight.bold,
                      )),
                ).paddingAll(AppSpaces.mediumPadding)
              ]
            : projectTasks
                .map((task) => _ProjectTasks(
                    task: task,
                    taskIndex: projectTasks.indexOf(task) + 1,
                    selectedDayValue: selectedDayValue,
                    selectedEmployee: selectedEmployee?.value))
                .toList(),
      ),
    );
  }
}

class _ProjectTasks extends StatelessWidget {
  final TaskModel task;
  final ValueNotifier<DateTime> selectedDayValue;
  final UserModel? selectedEmployee;
  final int taskIndex;

  const _ProjectTasks({
    required this.task,
    required this.selectedDayValue,
    required this.selectedEmployee,
    required this.taskIndex,
  });

  @override
  Widget build(BuildContext context) {
    //  key: ValueKey(task.id),
    //       confirmDismiss: (direction) async {
    //         if (UserModelHelper.isManager()) {
    //           return false;
    //         } else {
    //           return await showDialog(
    //               context: context, builder: (_) => DeleteTaskDialog(task: task));
    //         }
    //       },
    return Container(
      margin: const EdgeInsets.only(
          bottom: AppSpaces.mediumPadding,
          right: AppSpaces.smallPadding,
          left: AppSpaces.smallPadding),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppRadius.smallRadius),
          border: task.isApproved == true
              ? Border.all(color: ColorManager.successColor, width: 1)
              : Border.all(
                  color: ColorManager.secondaryColor.withOpacity(.1),
                  width: 1,
                )),
      child: Stack(
        children: [
          ListTile(
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //? Date
                Text(
                  task.createdAt?.toDate().formatDateToStringWithTime ?? '',
                  style: context.whiteHint.copyWith(
                    fontSize: 13,
                  ),
                ),

                context.smallGap,

                //? Task Description
                Text(
                  '$taskIndex. ${task.description}',
                  style: context.whiteSubTitle,
                ),
              ],
            ),
            contentPadding: EdgeInsets.symmetric(horizontal: 12),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // manager note
                if (task.managerNote != null && task.managerNote!.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(
                      top: AppSpaces.smallPadding,
                    ),
                    child: Text(
                      task.managerNote!,
                      style: context.whiteHint.copyWith(
                          fontSize: 13, color: ColorManager.primaryColor),
                    ),
                  ),

                if (task.attachmentUrl != null &&
                    task.attachmentUrl!.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(
                      top: AppSpaces.smallPadding,
                    ),
                    child: ClipRRect(
                        borderRadius:
                            BorderRadius.circular(AppRadius.smallRadius),
                        child: Image.network(
                          task.attachmentUrl!,
                          fit: BoxFit.cover,
                          height: 100,
                          width: 100,
                        ).onTap(() {
                          showDialog(
                            context: context,
                            builder: (_) => Dialog(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(
                                    AppRadius.smallRadius),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(
                                    AppRadius.smallRadius),
                                child: Image.network(
                                  task.attachmentUrl!,
                                  fit: BoxFit.cover,
                                  height: context.height * 0.8,
                                  width: context.width * 0.8,
                                ),
                              ),
                            ),
                          );
                        })),
                  )
              ],
            ),
            trailing: Padding(
              padding: const EdgeInsets.only(top: AppSpaces.mediumPadding),
              child: UserModelHelper.isManager()
                  ? AdminTaskActions(
                      task: task,
                      selectedEmployee: selectedEmployee,
                      selectedDayValue: selectedDayValue.value)
                  : EmployeeTaskActions(
                      task: task, selectedDayValue: selectedDayValue),
            ),
          ),
          if (task.isApproved == true)
            Align(
              alignment:
                  context.isAppEnglish ? Alignment.topRight : Alignment.topLeft,
              child: Container(
                padding: const EdgeInsets.all(AppSpaces.smallPadding - 2),
                margin: const EdgeInsets.symmetric(
                    horizontal: AppSpaces.xLargePadding),
                decoration: const BoxDecoration(
                  color: ColorManager.successColor,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(AppRadius.smallRadius),
                    bottomRight: Radius.circular(AppRadius.smallRadius),
                  ),
                ),
                child: Text(
                  context.tr.approved,
                  style: context.whiteLabelLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
