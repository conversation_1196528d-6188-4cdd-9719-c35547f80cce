import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/services/media/controller/media_controller.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/animated/lottie_icon.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/home/<USER>/widgets/floating_buttons/add_task_floating_button.dart';
import 'package:opti4t_tasks/src/screens/tasks/models/task_model.dart';
import 'package:opti4t_tasks/src/screens/tasks/view/widgets/close_task_dialog.dart';
import 'package:xr_helper/xr_helper.dart';

class EmployeeTaskActions extends ConsumerWidget {
  final TaskModel task;
  final ValueNotifier<DateTime> selectedDayValue;

  const EmployeeTaskActions(
      {super.key, required this.task, required this.selectedDayValue});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaController = ref.watch(mediaPickerControllerProvider);

    final completedTask = task.isCompleted;

    return Wrap(
      children: [
        //! Check Icon
        CircleAvatar(
                backgroundColor: completedTask
                    ? ColorManager.primaryColor
                    : ColorManager.secondaryColor,
                child: Icon(
                  completedTask ? Icons.check : CupertinoIcons.doc_checkmark,
                  color: Colors.white,
                ))
            .onTapWithRipple(() => showDialog(
                context: context,
                builder: (_) => CloseTaskDialog(
                      task: task,
                      selectedDayValue: selectedDayValue,
                    )))
            .paddingSymmetric(
                horizontal: completedTask ? 0 : AppSpaces.smallPadding),

        //! Edit Icon
        if (!task.isCompleted)
          const CircleAvatar(
                  backgroundColor: ColorManager.successColor,
                  child: BaseLottieIcon('assets/animated/edit.json',
                      width: 20, height: 20))
              .onTapWithRipple(
                  () => showDialog(
                        context: context,
                        builder: (_) => AddTaskDialog(
                          task: task,
                          parentTaskId: task.parentId,
                        ),
                      ).then((value) => mediaController.clearFiles()),
                  radius: 5)
      ],
    );
  }
}
