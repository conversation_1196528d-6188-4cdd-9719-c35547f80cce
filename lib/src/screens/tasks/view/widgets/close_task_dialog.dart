import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/shared_widgets.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/tasks/models/task_model.dart';
import 'package:opti4t_tasks/src/screens/tasks/providers/tasks_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class CloseTaskDialog extends HookConsumerWidget {
  final TaskModel? task;
  final ValueNotifier<DateTime> selectedDayValue;
  final bool closeAllTasks;

  const CloseTaskDialog({
    super.key,
    this.task,
    required this.selectedDayValue,
    this.closeAllTasks = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final taskController = ref.watch(taskControllerNotifierProvider(context));
    final completedTask = (task?.isCompleted ?? false);

    void closeTask() async {
      if (closeAllTasks) {
        await taskController.closeAllTasks(
          selectedDayValue: selectedDayValue,
        );
      } else {
        if (completedTask && task?.isApproved == true) {
          context.showBarMessage(context.tr.cannotReopenApprovedTask,
              isError: true);
          return;
        } else {
          if (completedTask) {
            await taskController.reOpenTask(
              task: task!,
            );
          } else {
            final copiedTask = task!.copyWith(closedAt: Timestamp.now());

            await taskController.editOrCloseTask(
              task: copiedTask,
              isClosed: completedTask ? false : true,
            );
          }
        }
      }
    }

    if (taskController.isLoading) {
      return const Center(
        child: LoadingWidget(),
      );
    }

    final title = completedTask
        ? context.tr.reopenTask
        : closeAllTasks
            ? context.tr.closeAllTasks
            : context.tr.closeTask;

    final description = completedTask
        ? context.tr.areYouSureYouWantToReopenThisTask
        : closeAllTasks
            ? context.tr.areYouSureYouWantToCloseAllTasks
            : context.tr.areYouSureYouWantToCloseThisTask;

    final buttonLabel = completedTask
        ? context.tr.reopenTask
        : closeAllTasks
            ? context.tr.closeAllTasks
            : context.tr.closeTask;

    return Dialog(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const CircleAvatar(
              backgroundColor: ColorManager.primaryColor,
              child: Icon(
                Icons.playlist_add_check_outlined,
                color: Colors.white,
              ),
            ),
            context.mediumGap,
            Text(
              title,
              style: context.whiteTitle,
            ),
          ],
        ),

        context.largeGap,

        //? Are you sure you want to close this task?
        Text(
          description,
          style: context.whiteSubTitle,
        ),

        context.xLargeGap,

        Row(
          children: [
            //? Cancel Button
            Expanded(
              child: TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(context.tr.cancel, style: context.whiteSubTitle),
              ),
            ),

            context.mediumGap,

            Expanded(
                flex: 2,
                child: Button(
                  label: buttonLabel,
                  onPressed: closeTask,
                  textColor: Colors.white,
                )),
          ],
        ).sized(
          height: 45,
        )
      ],
    ).paddingAll(AppSpaces.largePadding - 4));
  }
}
