import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti4t_tasks/src/core/consts/firestore_strings.dart';
import 'package:opti4t_tasks/src/core/services/firebase/firebase_storage_service.dart';
import 'package:opti4t_tasks/src/core/services/firebase/firestore_service.dart';
import 'package:opti4t_tasks/src/core/services/send_email/send_email_service.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/tasks/models/task_model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../employees/models/employee_request_model.dart';

final taskRepoProvider = Provider<TaskRepository>((ref) {
  return TaskRepository();
});

class TaskRepository with BaseRepository {
  //? Add Task To Firestore
  Future<void> addTask(
      {required TaskModel task,
      String? filePath,
      bool isParentTask = true}) async {
    await baseFunction(
      () async {
        if (filePath != null && filePath.isNotEmpty) {
          final url = await uploadFile(
            task: task,
            filePath: filePath,
          );

          task = task.copyWith(attachmentUrl: url, isParentTask: isParentTask);
        }

        await FirestoreService.createData(
          id: task.id,
          collection: FirebaseStrings.tasks,
          data: task.toJson(),
        );
      },
    );
  }

  Future<TaskModel?> getParentTaskByTitleAndDate({
    required String title,
    required String uid,
    required DateTime date,
  }) async {
    final tasks = await FirestoreService.getDataBy3Fields(
      collection: FirebaseStrings.tasks,
      field1: 'project',
      value1: title,
      field2: 'date',
      value2: date.formatDateToString,
      field3: 'uid',
      value3: uid,
      builder: (id, data) {
        return TaskModel.fromJson(id, data: data);
      },
    );

    return tasks.firstWhereOrNull((task) => task.isParentTask == true);
  }

  //? Update Task To Firestore
  Future<void> updateTask({
    required TaskModel task,
    String? filePath,
  }) async {
    await baseFunction(
      () async {
        if (filePath != null && filePath.isNotEmpty) {
          final url = await uploadFile(
            task: task,
            filePath: filePath,
          );

          task = task.copyWith(attachmentUrl: url);
        }

        await FirestoreService.updateData(
          collection: FirebaseStrings.tasks,
          id: task.id!,
          data: task.toJson(),
        );
      },
    );
  }

  //? upload file and get url
  Future<String> uploadFile({
    required TaskModel task,
    required String filePath,
  }) async {
    return await baseFunction(
      () async {
        final url = await FirebaseStorageService.uploadFile(
          fileName: task.project,
          filePath: filePath,
        );
        return url;
      },
    );
  }

  //? Reopen task
  Future<void> reOpenTask({
    required TaskModel task,
  }) async {
    await baseFunction(
      () async {
        await FirestoreService.updateData(
          collection: FirebaseStrings.tasks,
          id: task.id!,
          data: task.toJsonReopen(),
        );
      },
    );
  }

  //? Get Task Data From Same Login Id From Firestore
  Future<List<TaskModel>> getTasks(
      {required DateTime date, DateTime? toDate, String? userId}) async {
    try {
      List<TaskModel> tasks = [];

      if (UserModelHelper.isManager() && userId == null) return [];

      if (toDate == null) {
        tasks = await FirestoreService.getDataBy2Fields(
            collection: FirebaseStrings.tasks,
            field1: FirebaseStrings.uid,
            value1: userId ?? UserModelHelper.localUID(),
            field2: FirebaseStrings.date,
            value2: date.formatDateToString,
            builder: (id, data) {
              return TaskModel.fromJson(id, data: data);
            });
      } else {
        tasks = await FirestoreService.getDataBy3Fields(
            collection: FirebaseStrings.tasks,
            field1: FirebaseStrings.uid,
            value1: userId ?? UserModelHelper.localUID(),
            field2: FirebaseStrings.date,
            value2: date.formatDateToString,
            field3: FirebaseStrings.date,
            value3: toDate.formatDateToString,
            builder: (id, data) {
              return TaskModel.fromJson(id, data: data);
            });

        //? sort tasks by date
        tasks.sort((a, b) => a.date!.compareTo(b.date!));
      }
      return tasks;
    } catch (e) {
      rethrow;
    }
  }

  Stream<List<TaskModel>> getTasksStream({
    required DateTime date,
    String? userId,
  }) {
    return FirestoreService.getStreamDataBy2Fields(
      collection: FirebaseStrings.tasks,
      field1: FirebaseStrings.uid,
      value1: userId ?? UserModelHelper.localUID(),
      field2: FirebaseStrings.date,
      value2: date.formatDateToString,
      builder: (id, data) {
        return TaskModel.fromJson(id, data: data);
      },
    );
  }

  //? Get Un closed tasks stream
  Stream<List<TaskModel>> getUnClosedTasksStream({
    required DateTime date,
    String? userId,
  }) {
    return FirestoreService.getStreamDataBy2Fields(
      collection: FirebaseStrings.tasks,
      field1: FirebaseStrings.uid,
      value1: userId ?? UserModelHelper.localUID(),
      field2: FirebaseStrings.closedAt,
      value2: null,
      builder: (id, data) {
        //? return only tasks that created after the date
        if (data['createdAt'] != null) {
          final createdAt = data['createdAt'] as Timestamp;
          final createdAtDate = createdAt.toDate();
          if (createdAtDate.isAfter(date) || createdAtDate.isToday()) {
            return TaskModel.empty();
          }
        }

        return TaskModel.fromJson(id, data: data);
      },
    );
  }

  Future<void> reviewTask({
    required TaskModel task,
    required String review,
  }) async {
    await baseFunction(
      () async {
        await FirestoreService.updateData(
          collection: FirebaseStrings.tasks,
          id: task.id!,
          data: {
            'manager_note': review,
          },
        );

        final employeeEmail = task.userEmail;

        // final Email email = Email(
        //   subject:
        //       '${task.project} - ${task.date.formatDateToString} Task Review',
        //   body: review,
        //   recipients: [employeeEmail],
        // );
        //
        // await FlutterEmailSender.send(email);

        await SendEmailService.sendEmail(
            subject:
                '${task.description} - ${task.date.formatDateToString} Task Review',
            body: review,
            recipient: [employeeEmail]);
      },
    );
  }

  Future<void> reviewRequest({
    required EmployeeRequestModel request,
    required String review,
  }) async {
    await baseFunction(
      () async {
        await FirestoreService.updateData(
          collection: FirebaseStrings.employeeRequests,
          id: request.id!,
          data: {
            'manager_note': review,
          },
        );

        final employeeEmail = request.userEmail;

        await SendEmailService.sendEmail(
            subject:
                '${request.description} - ${request.date.formatDateToString} Task Review',
            body: review,
            recipient: [employeeEmail]);
      },
    );
  }

  //? Approve Tasks
  Future<void> approveTasks({
    required List<TaskModel> tasks,
  }) async {
    await baseFunction(
      () async {
        for (final task in tasks) {
          await FirestoreService.updateData(
            collection: FirebaseStrings.tasks,
            id: task.id!,
            data: task.toJsonApprove(),
          );
        }
      },
    );
  }

  //? Delete Task
  Future<void> deleteTask({
    required TaskModel task,
  }) async {
    await baseFunction(
      () async {
        FirestoreService.deleteData(
          collection: FirebaseStrings.tasks,
          id: task.id!,
        );

        //? Delete file
        if (task.attachmentUrl != null && task.attachmentUrl!.isNotEmpty) {
          await FirebaseStorageService.deleteFileByUrl(
            url: task.attachmentUrl!,
          );
        }
      },
    );
  }

  //? Close All Tasks
  Future<void> closeAllTasks({
    required DateTime date,
  }) async {
    await baseFunction(
      () async {
        final tasks = await FirestoreService.getDataBy2Fields(
            collection: FirebaseStrings.tasks,
            field1: FirebaseStrings.uid,
            value1: UserModelHelper.localUID(),
            field2: FirebaseStrings.date,
            value2: date.formatDateToString,
            builder: (id, data) {
              return TaskModel.fromJson(id, data: data);
            });

        for (final task in tasks) {
          final copiedTask = task.copyWith(closedAt: Timestamp.now());

          await FirestoreService.updateData(
            collection: FirebaseStrings.tasks,
            id: copiedTask.id!,
            data: copiedTask.toJson(),
          );
        }

        // final unClosedTasks = await FirestoreService.getDataBy2Fields(
        //     collection: FirebaseStrings.tasks,
        //     field1: FirebaseStrings.uid,
        //     value1: UserModelHelper.localUID(),
        //     field2: FirebaseStrings.closedAt,
        //     value2: null,
        //     builder: (id, data) {
        //       return TaskModel.fromJson(id, data: data);
        //     });

        // log('UnCLOSSSSEED ${unClosedTasks.length}');

        // for (final task in unClosedTasks) {
        //   final copiedTask = task.copyWith(closedAt: Timestamp.now());
        //
        //   FirestoreService.updateData(
        //     collection: FirebaseStrings.tasks,
        //     id: copiedTask.id!,
        //     data: copiedTask.toJson(),
        //   );
        // }
      },
    );
  }
}
