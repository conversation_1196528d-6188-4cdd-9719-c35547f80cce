import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/screens/reports/repositories/report_repository.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../auth/models/user_model.dart';

// * Provider ========================================
final reportControllerProvider =
    Provider.family<ReportController, BuildContext>(
  (ref, context) {
    final reportRepo = ref.watch(reportRepoProvider);

    return ReportController(
      context,
      reportRepo: reportRepo,
    );
  },
);

// * Change Notifier Provider ========================================
final reportControllerNotifierProvider =
    ChangeNotifierProvider.family<ReportController, BuildContext>(
  (ref, context) {
    final reportRepo = ref.watch(reportRepoProvider);

    return ReportController(
      context,
      reportRepo: reportRepo,
    );
  },
);

class ReportController extends BaseVM {
  final BuildContext context;
  final ReportRepository reportRepo;

  ReportController(
    this.context, {
    required this.reportRepo,
  });

  //! Generate Report
  Future<String> generateReport(
    BuildContext context, {
    required UserModel employee,
    required String recipientEmail,
    required (DateTime from, DateTime to) selectedDates,
    required String reportType,
    required String selectedVacationStatus,
    bool isExcel = false,
  }) async {
    return await baseFunction(
      context,
      () async {
        var pdfPath;

        if (reportType == context.tr.absentReport) {
          pdfPath = await reportRepo.generateAbsentReport(
            context,
            employee: employee,
            recipientEmail: recipientEmail,
            selectedDates: selectedDates,
            reportType: reportType,
            isExcel: isExcel,
          );
        } else if (reportType == context.tr.tasksReport) {
          pdfPath = await reportRepo.generateTasksReport(
            context,
            employee: employee,
            recipientEmail: recipientEmail,
            selectedDates: selectedDates,
            reportType: reportType,
            isExcel: isExcel,
          );
        } else if (reportType == context.tr.kpiPercentReport) {
          pdfPath = await reportRepo.generatePercentKPIReport(
            context,
            employee: employee,
            recipientEmail: recipientEmail,
            selectedDates: selectedDates,
            reportType: reportType,
            isExcel: isExcel,
          );
        } else if (reportType == context.tr.vacationsReport) {
          pdfPath = await reportRepo.generateVacationReport(
            context,
            employee: employee,
            recipientEmail: recipientEmail,
            selectedDates: selectedDates,
            reportType: reportType,
            isExcel: isExcel,
            selectedVacationStatus: selectedVacationStatus,
          );
        } else if (reportType == context.tr.attendanceReport) {
          pdfPath = await reportRepo.generateAttendanceReport(
            context,
            employee: employee,
            recipientEmail: recipientEmail,
            selectedDates: selectedDates,
            reportType: reportType,
            isExcel: isExcel,
          );
        } else {
          //! KPI Report
          pdfPath = await reportRepo.generateKPIReport(
            context,
            employee: employee,
            recipientEmail: recipientEmail,
            selectedDates: selectedDates,
            reportType: reportType,
            isExcel: isExcel,
          );
        }

        return pdfPath;
      },
    );
  }

  //! Open PDF
  Future<void> openPDF(String path) async {
    await baseFunction(
      context,
      () async {
        await reportRepo.openPDF(path);
      },
    );
  }

  //! Open Excel
  Future<void> openExcel(String path) async {
    await baseFunction(
      context,
      () async {
        await reportRepo.openExcel(path);
      },
    );
  }

  //! Send Email
  Future<void> sendEmail({
    required UserModel employee,
    required String recipientEmail,
    required DateTime fromDate,
    required DateTime toDate,
    required String generatedPDFReportPath,
  }) async {
    await baseFunction(
      context,
      () async {
        await reportRepo.sendEmail(
          employee: employee,
          recipientEmail: recipientEmail,
          fromDate: fromDate,
          toDate: toDate,
          generatedPDFReportPath: generatedPDFReportPath,
        );
      },
    );
  }
}
