import 'dart:developer';
import 'dart:io';

import 'package:collection/collection.dart';
import 'package:opti4t_tasks/src/screens/attendance/models/attendance_model.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/reports/repositories/pdf_reports/attendance/attendance_report_helper.dart';
import 'package:opti4t_tasks/src/screens/vacations/models/vacation_model.dart';
import 'package:path_provider/path_provider.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart' hide Column;
import 'package:xr_helper/xr_helper.dart';

Future<String> generateAllEmployeesAttendanceReportExcel({
  required List<UserModel?> allEmployees,
  required List<AttendanceModel> attendances,
  required List<VacationModel> allVacations,
  required DateTime fromDate,
  required DateTime toDate,
  required bool isEnglish,
}) async {
  final Workbook workbook = Workbook();
  final Worksheet sheet = workbook.worksheets[0];
  final Style headerStyle = workbook.styles.add('HeaderStyle');
  final Style dataStyle = workbook.styles.add('DataStyle');

  // Define styles
  headerStyle.hAlign = HAlignType.center;
  headerStyle.vAlign = VAlignType.center;
  headerStyle.bold = true;
  dataStyle.hAlign = HAlignType.center;
  dataStyle.vAlign = VAlignType.center;
  dataStyle.wrapText = true;

  // Define headers based on language
  List<String> headers = isEnglish
      ? [
          'Employee',
          'Date',
          'Check-Ins',
          'Partial Check-Outs',
          'Check-Out',
          'Total Attendance Time',
          'Total Partial Times'
        ]
      : [
          'الموظف',
          'التاريخ',
          'وقت الدخول',
          'وقت الخروج الجزئي',
          'وقت الخروج',
          'إجمالي وقت الحضور',
          'إجمالي أوقات الخروج الجزئي'
        ];

  // Write headers
  for (int columnIndex = 1; columnIndex <= headers.length; columnIndex++) {
    final Range headerCell = sheet.getRangeByIndex(1, columnIndex);
    headerCell.setText(headers[columnIndex - 1]);
    headerCell.cellStyle = headerStyle;
  }

  int rowIndex = 2;

  for (var emp in allEmployees) {
    for (var date = fromDate;
        date.isBefore(toDate) || date.isSameDay(toDate);
        date = date.add(Duration(days: 1))) {
      var attendance = attendances.firstWhereOrNull(
        (att) => att.uid == emp?.uid && att.date?.isSameDay(date) == true,
      );

      var vacationsForEmployee = allVacations
          .where((vacation) =>
              vacation.uid == emp?.uid &&
              vacation.isApproved == true &&
              vacation.fromDate != null &&
              vacation.toDate != null &&
              (vacation.fromDate!.isBefore(date) ||
                  vacation.fromDate!.isSameDay(date)) &&
              (vacation.toDate!.isAfter(date) ||
                  vacation.toDate!.isSameDay(date)))
          .toList();

      final isOnVacation = vacationsForEmployee.isNotEmpty;

      final checkIns = isOnVacation
          ? (isEnglish ? 'Vacation' : 'إجازة')
          : attendance?.checkedInAt.join('\n') ??
              (isEnglish ? 'Absent' : 'غائب');
      final partCheckOuts = isOnVacation
          ? '-'
          : attendance?.partCheckoutReason.entries
                  .map((e) => e.key)
                  .join('\n') ??
              '-';
      final checkOut = isOnVacation ? '-' : attendance?.checkedOutAt ?? '-';
      final totalAttendanceTime = isOnVacation
          ? '-'
          : attendance != null
              ? formatDuration(
                  calculateTotalAttendanceTime(attendance), !isEnglish)
              : '-';
      final totalPartialTimes = isOnVacation
          ? '-'
          : attendance != null
              ? formatDuration(
                  calculateTotalPartialTimes(attendance), !isEnglish)
              : '-';

      final employeeNameOrEmail = emp?.name ?? emp?.email ?? '-';

      List<String> rowData = isEnglish
          ? [
              employeeNameOrEmail,
              date.formatDateToString,
              checkIns,
              partCheckOuts,
              checkOut,
              totalAttendanceTime,
              totalPartialTimes,
            ]
          : [
              employeeNameOrEmail,
              date.formatDateToString,
              checkIns,
              partCheckOuts,
              checkOut,
              totalAttendanceTime,
              totalPartialTimes,
            ];

      for (int columnIndex = 1; columnIndex <= rowData.length; columnIndex++) {
        final Range cell = sheet.getRangeByIndex(rowIndex, columnIndex);
        cell.setText(rowData[columnIndex - 1]);
        cell.cellStyle = dataStyle;
      }
      rowIndex++;
    }
  }

  // Auto-fit columns for a better view
  for (int columnIndex = 1; columnIndex <= headers.length; columnIndex++) {
    sheet.autoFitColumn(columnIndex);
  }

  // Save the Excel file
  final Directory appDocDir = await getApplicationDocumentsDirectory();
  final String appDocPath = appDocDir.path;
  final String path =
      '$appDocPath/AllEmployeesAttendanceReport_${DateTime.now().toIso8601String()}.xlsx';

  final List<int> bytes = workbook.saveAsStream();
  workbook.dispose();

  final File file = File(path);
  await file.writeAsBytes(bytes);

  print('All employees attendance report Excel sheet saved at $path');

  return path;
}

Future<String> generateSingleEmployeeAttendanceReportExcel({
  required UserModel employee,
  required List<AttendanceModel> attendances,
  required List<VacationModel> allVacations,
  required DateTime fromDate,
  required DateTime toDate,
  required bool isEnglish,
}) async {
  final Workbook workbook = Workbook();
  final Worksheet sheet = workbook.worksheets[0];
  final Style headerStyle = workbook.styles.add('HeaderStyle');
  final Style dataStyle = workbook.styles.add('DataStyle');

  // Define styles
  headerStyle.hAlign = HAlignType.center;
  headerStyle.vAlign = VAlignType.center;
  headerStyle.bold = true;
  dataStyle.hAlign = HAlignType.center;
  dataStyle.vAlign = VAlignType.center;
  dataStyle.wrapText = true;

  // Define headers based on language
  List<String> headers = isEnglish
      ? [
          'Date',
          'Check-Ins',
          'Partial Check-Outs',
          'Check-Out',
          'Total Attendance Time',
          'Total Partial Times'
        ]
      : [
          'التاريخ',
          'وقت الدخول',
          'وقت الخروج الجزئي',
          'وقت الخروج',
          'إجمالي وقت الحضور',
          'إجمالي أوقات الخروج الجزئي'
        ];

  // Write headers
  for (int columnIndex = 1; columnIndex <= headers.length; columnIndex++) {
    final Range headerCell = sheet.getRangeByIndex(1, columnIndex);
    headerCell.setText(headers[columnIndex - 1]);
    headerCell.cellStyle = headerStyle;
  }

  int rowIndex = 2;

  for (var date = fromDate;
      date.isBefore(toDate) || date.isSameDay(toDate);
      date = date.add(Duration(days: 1))) {
    var attendance = attendances.firstWhereOrNull(
      (att) => att.date?.isSameDay(date) == true,
    );

    var vacationsForEmployee = allVacations
        .where((vacation) =>
            vacation.uid == employee.uid &&
            vacation.isApproved == true &&
            vacation.fromDate != null &&
            vacation.toDate != null &&
            (vacation.fromDate!.isBefore(date) ||
                vacation.fromDate!.isSameDay(date)) &&
            (vacation.toDate!.isAfter(date) ||
                vacation.toDate!.isSameDay(date)))
        .toList();

    final isOnVacation = vacationsForEmployee.isNotEmpty;

    final checkIns = isOnVacation
        ? (isEnglish ? 'Vacation' : 'إجازة')
        : attendance?.checkedInAt.join('\n') ?? (isEnglish ? 'Absent' : 'غائب');
    final partCheckOuts = isOnVacation
        ? '-'
        : attendance?.partCheckoutReason.entries.map((e) => e.key).join('\n') ??
            '-';
    final checkOut = isOnVacation ? '-' : attendance?.checkedOutAt ?? '-';
    final totalAttendanceTime = isOnVacation
        ? '-'
        : attendance != null
            ? formatDuration(
                calculateTotalAttendanceTime(attendance), !isEnglish)
            : '-';
    final totalPartialTimes = isOnVacation
        ? '-'
        : attendance != null
            ? formatDuration(calculateTotalPartialTimes(attendance), !isEnglish)
            : '-';

    List<String> rowData = isEnglish
        ? [
            date.formatDateToString,
            checkIns,
            partCheckOuts,
            checkOut,
            totalAttendanceTime,
            totalPartialTimes,
          ]
        : [
            date.formatDateToString,
            checkIns,
            partCheckOuts,
            checkOut,
            totalAttendanceTime,
            totalPartialTimes,
          ];

    for (int columnIndex = 1; columnIndex <= rowData.length; columnIndex++) {
      final Range cell = sheet.getRangeByIndex(rowIndex, columnIndex);
      cell.setText(rowData[columnIndex - 1]);
      cell.cellStyle = dataStyle;
    }
    rowIndex++;
  }

  // Auto-fit columns for a better view
  for (int columnIndex = 1; columnIndex <= headers.length; columnIndex++) {
    sheet.autoFitColumn(columnIndex);
  }

  // Save the Excel file
  final Directory appDocDir = await getApplicationDocumentsDirectory();
  final String appDocPath = appDocDir.path;
  final String path =
      '$appDocPath/SingleEmployeeAttendanceReport_${DateTime.now().toIso8601String()}.xlsx';

  final List<int> bytes = workbook.saveAsStream();
  workbook.dispose();

  final File file = File(path);
  await file.writeAsBytes(bytes);

  log('Single employee attendance report Excel sheet saved at $path');

  return path;
}

// Future<String> generateAllEmployeesAttendanceReportExcel({
//   required List<UserModel?> allEmployeesList,
//   required List<AttendanceModel> attendances,
//   required DateTime fromDate,
//   required DateTime toDate,
//   required bool isEnglish,
// }) async {
//   final filteredAllEmployees = allEmployeesList
//       .where(
//         (element) => element?.isActive == true,
//       )
//       .toList();
//
//   final Workbook workbook = Workbook();
//   final Worksheet sheet = workbook.worksheets[0];
//   final Style headerStyle = workbook.styles.add('HeaderStyle');
//   final Style dataStyle = workbook.styles.add('DataStyle');
//
//   // Define styles
//   headerStyle.hAlign = HAlignType.center;
//   headerStyle.vAlign = VAlignType.center;
//   headerStyle.bold = true;
//   dataStyle.hAlign = HAlignType.center;
//   dataStyle.vAlign = VAlignType.center;
//   dataStyle.wrapText = true;
//
//   // Define headers based on language
//   List<String> headers = isEnglish
//       ? [
//           'Employee',
//           'Date',
//           'Check-Ins',
//           'Partial\nCheck-Outs',
//           'Total\nPartial Times',
//           'Check-Out',
//           'Total\nAttendance Time',
//         ]
//       : [
//           'الموظف',
//           'التاريخ',
//           'وقت الدخول',
//           'وقت\nالخروج الجزئي',
//           'إجمالي أوقات\nالخروج الجزئي',
//           'وقت الخروج',
//           'إجمالي\nوقت الحضور',
//         ];
//
//   // Write headers
//   for (int columnIndex = 1; columnIndex <= headers.length; columnIndex++) {
//     final Range headerCell = sheet.getRangeByIndex(1, columnIndex);
//     headerCell.setText(headers[columnIndex - 1]);
//     headerCell.cellStyle = headerStyle;
//   }
//
//   int rowIndex = 2;
//
//   for (var emp in filteredAllEmployees) {
//     var attendance = attendances.firstWhereOrNull(
//       (att) =>
//           att.uid == emp?.uid &&
//           att.date?.isAfter(fromDate) == true &&
//           att.date?.isBefore(toDate) == true,
//     );
//
//     final checkIns = attendance?.checkedInAt.isNotEmpty == true
//         ? attendance?.checkedInAt.join('\n') ?? '-'
//         : (isEnglish ? 'Absent' : 'غائب');
//     final partCheckOuts = attendance?.partCheckoutReason.isNotEmpty == true
//         ? attendance?.partCheckoutReason.entries
//                 .map((e) => '${e.key}: ${e.value}')
//                 .join('\n') ??
//             '-'
//         : (isEnglish ? 'Absent' : 'غائب');
//     final checkOut =
//         attendance?.checkedOutAt ?? (isEnglish ? 'Absent' : 'غائب');
//     final date = attendance?.date?.formatDateToString ?? '-';
//     final totalAttendanceTime = attendance != null
//         ? formatDuration(calculateTotalAttendanceTime(attendance), !isEnglish)
//         : (isEnglish ? 'Absent' : 'غائب');
//     final totalPartialTimes = attendance != null
//         ? formatDuration(calculateTotalPartialTimes(attendance), !isEnglish)
//         : (isEnglish ? 'Absent' : 'غائب');
//
//     final employeeNameOrEmail = emp?.name ?? emp?.email ?? '-';
//
//     List<String> rowData = isEnglish
//         ? [
//             employeeNameOrEmail,
//             date,
//             checkIns,
//             partCheckOuts,
//             totalPartialTimes,
//             checkOut,
//             totalAttendanceTime,
//           ]
//         : [
//             employeeNameOrEmail,
//             date,
//             checkIns,
//             partCheckOuts,
//             totalPartialTimes,
//             checkOut,
//             totalAttendanceTime,
//           ];
//
//     for (int columnIndex = 1; columnIndex <= rowData.length; columnIndex++) {
//       final Range cell = sheet.getRangeByIndex(rowIndex, columnIndex);
//       cell.setText(rowData[columnIndex - 1]);
//       cell.cellStyle = dataStyle;
//       if (!isEnglish) {
//         // cell.textDirection = TextDirectionType.rightToLeft;
//       }
//     }
//     rowIndex++;
//   }
//
//   // Auto-fit columns for a better view
//   for (int columnIndex = 1; columnIndex <= headers.length; columnIndex++) {
//     sheet.autoFitColumn(columnIndex);
//   }
//
//   // Save the Excel file
//   final Directory appDocDir = await getApplicationDocumentsDirectory();
//   final String appDocPath = appDocDir.path;
//   final String path =
//       '$appDocPath/AllEmployeesAttendanceReport_${DateTime.now().toIso8601String()}.xlsx';
//
//   final List<int> bytes = workbook.saveAsStream();
//   workbook.dispose();
//
//   final File file = File(path);
//   await file.writeAsBytes(bytes);
//
//   print('All employees attendance report Excel sheet saved at $path');
//
//   return path;
// }

// Future<String> generateSingleEmployeeAttendanceReportExcel({
//   required UserModel employee,
//   required List<AttendanceModel> attendances,
//   required DateTime fromDate,
//   required DateTime toDate,
//   required bool isEnglish,
// }) async {
//   final Workbook workbook = Workbook();
//   final Worksheet sheet = workbook.worksheets[0];
//   final Style headerStyle = workbook.styles.add('HeaderStyle');
//   final Style dataStyle = workbook.styles.add('DataStyle');
//
//   // Define styles
//   headerStyle.hAlign = HAlignType.center;
//   headerStyle.vAlign = VAlignType.center;
//   headerStyle.bold = true;
//   dataStyle.hAlign = HAlignType.center;
//   dataStyle.vAlign = VAlignType.center;
//   dataStyle.wrapText = true;
//
//   // Define headers based on language
//   List<String> headers = isEnglish
//       ? [
//           'Date',
//           'Check-Ins',
//           'Partial Check-Outs',
//           'Check-Out',
//           'Total Attendance Time',
//           'Total Partial Times'
//         ]
//       : [
//           'التاريخ',
//           'وقت الدخول',
//           'وقت الخروج الجزئي',
//           'وقت الخروج',
//           'إجمالي وقت الحضور',
//           'إجمالي أوقات الخروج الجزئي'
//         ];
//
//   // Write headers
//   for (int columnIndex = 1; columnIndex <= headers.length; columnIndex++) {
//     final Range headerCell = sheet.getRangeByIndex(1, columnIndex);
//     headerCell.setText(headers[columnIndex - 1]);
//     headerCell.cellStyle = headerStyle;
//   }
//
//   int rowIndex = 2;
//
//   for (var attendance in attendances) {
//     final checkIns = attendance.checkedInAt.join('\n');
//     final partCheckOuts = attendance.partCheckoutReason.entries
//         .map((e) => '${e.key}: ${e.value}')
//         .join('\n');
//     final checkOut = attendance.checkedOutAt ?? (isEnglish ? 'absent' : 'غائب');
//     final totalAttendanceTime =
//         formatDuration(calculateTotalAttendanceTime(attendance), !isEnglish);
//     final totalPartialTimes =
//         formatDuration(calculateTotalPartialTimes(attendance), !isEnglish);
//
//     List<String> rowData = isEnglish
//         ? [
//             attendance.date?.formatDateToString ?? '-',
//             checkIns,
//             partCheckOuts,
//             checkOut,
//             totalAttendanceTime,
//             totalPartialTimes,
//           ]
//         : [
//             attendance.date?.formatDateToString ?? '-',
//             checkIns,
//             partCheckOuts,
//             checkOut,
//             totalAttendanceTime,
//             totalPartialTimes,
//           ];
//
//     for (int columnIndex = 1; columnIndex <= rowData.length; columnIndex++) {
//       final Range cell = sheet.getRangeByIndex(rowIndex, columnIndex);
//       cell.setText(rowData[columnIndex - 1]);
//       cell.cellStyle = dataStyle;
//       if (!isEnglish) {
//         // cell.textDirection = TextDirectionType.rightToLeft;
//       }
//     }
//     rowIndex++;
//   }
//
//   // Auto-fit columns for a better view
//   for (int columnIndex = 1; columnIndex <= headers.length; columnIndex++) {
//     sheet.autoFitColumn(columnIndex);
//   }
//
//   // Save the Excel file
//   final Directory appDocDir = await getApplicationDocumentsDirectory();
//   final String appDocPath = appDocDir.path;
//   final String path =
//       '$appDocPath/SingleEmployeeAttendanceReport_${DateTime.now().toIso8601String()}.xlsx';
//
//   final List<int> bytes = workbook.saveAsStream();
//   workbook.dispose();
//
//   final File file = File(path);
//   await file.writeAsBytes(bytes);
//
//   log('Single employee attendance report Excel sheet saved at $path');
//
//   return path;
// }
