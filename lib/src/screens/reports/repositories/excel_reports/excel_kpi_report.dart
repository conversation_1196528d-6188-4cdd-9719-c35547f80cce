import 'dart:io';

import 'package:intl/intl.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/tasks/models/task_model.dart';
import 'package:path_provider/path_provider.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart';
import 'package:xr_helper/xr_helper.dart';

Future<String> generateKPIReportExcel({
  required UserModel employee,
  required List<TaskModel> tasks,
  required DateTime fromDate,
  required DateTime toDate,
  required bool isEnglish,
}) async {
  final Workbook workbook = Workbook();
  final Worksheet sheet = workbook.worksheets[0];
  final Style headerStyle = workbook.styles.add('HeaderStyle');
  final Style dataStyle = workbook.styles.add('DataStyle');

  // Define styles
  headerStyle.hAlign = HAlignType.center;
  headerStyle.bold = true;
  dataStyle.hAlign = HAlignType.center;

  // Define headers
  if (isEnglish) {
    sheet.getRangeByName('A1').setText('Date');
    sheet.getRangeByName('B1').setText('KPI');
    sheet.getRangeByName('C1').setText('Status');
  } else {
    sheet.getRangeByName('A1').setText('التاريخ');
    sheet.getRangeByName('B1').setText('نسبة الأداء');
    sheet.getRangeByName('C1').setText('الحالة');
  }

  sheet.getRangeByName('A1').cellStyle = headerStyle;
  sheet.getRangeByName('B1').cellStyle = headerStyle;
  sheet.getRangeByName('C1').cellStyle = headerStyle;

  // Group tasks by date
  Map<DateTime, List<TaskModel>> tasksByDate = {};
  for (var task in tasks) {
    var date = task.createdAt?.toDate();
    if (date != null) {
      if (!tasksByDate.containsKey(date)) {
        tasksByDate[date] = [];
      }
      tasksByDate[date]?.add(task);
    }
  }

  int rowIndex = 2;
  tasksByDate.forEach((date, tasks) {
    int closedTasksCount = tasks.where((task) => task.isCompleted).length;
    double closedTasksPercentage = (closedTasksCount / tasks.length) * 100;
    String status = closedTasksPercentage > 85 ? 'Approved' : 'Not Approved';

    sheet
        .getRangeByIndex(rowIndex, 1)
        .setText(DateFormat('yyyy-MM-dd', 'en').format(date));
    sheet
        .getRangeByIndex(rowIndex, 2)
        .setText('${closedTasksPercentage.toStringAsFixed(0)}%');
    sheet.getRangeByIndex(rowIndex, 3).setText(status);

    sheet.getRangeByIndex(rowIndex, 1).cellStyle = dataStyle;
    sheet.getRangeByIndex(rowIndex, 2).cellStyle = dataStyle;
    sheet.getRangeByIndex(rowIndex, 3).cellStyle = dataStyle;

    rowIndex++;
  });

  // Auto-fit columns
  sheet.autoFitColumn(1);
  sheet.autoFitColumn(2);
  sheet.autoFitColumn(3);

  // Save the Excel file
  final Directory appDocDir = await getApplicationDocumentsDirectory();
  final String appDocPath = appDocDir.path;
  final String path =
      '$appDocPath/KPIReport_${DateTime.now().toIso8601String()}.xlsx';

  final List<int> bytes = workbook.saveAsStream();
  workbook.dispose();

  final File file = File(path);
  await file.writeAsBytes(bytes);

  Log.w('KPI report Excel sheet saved at $path');

  return path;
}
