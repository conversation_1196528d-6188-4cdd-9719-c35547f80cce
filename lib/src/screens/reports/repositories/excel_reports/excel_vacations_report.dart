import 'dart:io';

import 'package:opti4t_tasks/src/core/consts/app_constants.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/vacations/models/vacation_model.dart';
import 'package:path_provider/path_provider.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart' hide Column;
import 'package:syncfusion_flutter_xlsio/xlsio.dart';
import 'package:xr_helper/xr_helper.dart';

Future<String> generateVacationReportExcel({
  required List<VacationModel> vacations,
  required UserModel employee,
  required bool isEnglish,
  required String selectedVacationStatus,
  required DateTime fromDate,
  required DateTime toDate,
}) async {
  final isAllEmployees = employee.uid == AppConsts.allEmployees;

  final isAll = selectedVacationStatus == 'all';
  final isApproved = selectedVacationStatus == 'approved';
  final isRejected = selectedVacationStatus == 'rejected';

  final filteredVacations = isAllEmployees
      ? vacations
      : vacations.where((vacation) {
          return vacation.uid == employee.uid;
        }).toList();

  // Create a new Excel document.
  final Workbook workbook = Workbook();
  final Worksheet sheet = workbook.worksheets[0];

  // Define headers for both English and Arabic
  final List<String> headersEnglish = [
    'Employee Name',
    'Vacation Start Date',
    'Vacation End Date',
    'Reason',
    'Approval Status',
    'Days'
  ];

  final List<String> headersArabic = [
    'اسم الموظف',
    'تاريخ بداية الإجازة',
    'تاريخ نهاية الإجازة',
    'السبب',
    'حالة الموافقة',
    'الأيام'
  ];

  // Choose headers based on isEnglish flag
  final List<String> headers = isEnglish ? headersEnglish : headersArabic;

  // Apply header styles and set headers
  for (int columnIndex = 1; columnIndex <= headers.length; columnIndex++) {
    final Range headerCell = sheet.getRangeByIndex(1, columnIndex);
    headerCell.setText(headers[columnIndex - 1]);
    headerCell.cellStyle.hAlign =
        isEnglish ? HAlignType.center : HAlignType.right;
    headerCell.cellStyle.bold = true;
  }

  // Fill in vacation data
  for (int i = 0; i < filteredVacations.length; i++) {
    final VacationModel vacation = filteredVacations[i];
    final int days = (vacation.toDate
                ?.difference(vacation.fromDate ??
                    vacation.createdAt?.toDate() ??
                    DateTime.now())
                .inDays ??
            0) +
        1;
    final List<String> rowData = [
      vacation.userName,
      vacation.fromDate.formatDateToString,
      vacation.toDate.formatDateToString,
      vacation.reason.isEmpty ? 'N/A' : vacation.reason,
      vacation.isApproved == true
          ? isEnglish
              ? 'Approved'
              : 'معتمدة'
          : isEnglish
              ? 'Not Approved'
              : 'غير معتمدة',
      days.toString()
    ];

    for (int columnIndex = 1; columnIndex <= rowData.length; columnIndex++) {
      final Range cell = sheet.getRangeByIndex(i + 2, columnIndex);
      cell.setText(rowData[columnIndex - 1]);
      cell.cellStyle.hAlign = isEnglish ? HAlignType.center : HAlignType.right;
      cell.cellStyle.wrapText = true;
    }
  }

  // Set specific width for "Employee Name" column
  sheet.getRangeByIndex(1, 1, filteredVacations.length + 1, 1).columnWidth = 25;

  // Auto-fit the other columns to adjust to the content
  for (int columnIndex = 2; columnIndex <= headers.length; columnIndex++) {
    sheet.autoFitColumn(columnIndex);
  }

  // Save the Excel file
  final Directory appDocDir = await getApplicationDocumentsDirectory();
  final String appDocPath = appDocDir.path;
  final pathName = isAll
      ? 'All'
      : isApproved
          ? 'Approved'
          : isRejected
              ? 'Rejected'
              : 'Pending';

  final String path =
      '$appDocPath/${pathName}_VacationReport_${DateTime.now().toIso8601String()}.xlsx';

  final List<int> bytes = workbook.saveAsStream();
  workbook.dispose();

  final File file = File(path);
  await file.writeAsBytes(bytes);
  print('Vacation report Excel sheet saved at $path');

  return path;
}
