import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/tasks/models/task_model.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/src/widgets/font.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:xr_helper/xr_helper.dart';

import '../report_repository_helper.dart';

Future<pw.Document> generateKPIReportPDF(BuildContext appContext,
    {required UserModel employee,
    required List<TaskModel> tasks,
    required DateTime fromDate,
    required DateTime toDate}) async {
  final arabicFont = Font.ttf(
    await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
  );

  final pdf = pw.Document();

  arabicTable() {
    // Group tasks by date
    Map<DateTime, List<TaskModel>> tasksByDate = {};
    for (var task in tasks) {
      var date = task.createdAt?.toDate();
      if (date != null) {
        if (!tasksByDate.containsKey(date)) {
          tasksByDate[date] = [];
        }
        tasksByDate[date]?.add(task);
      }
    }

    return pw.Table(
      columnWidths: {
        0: const pw.FlexColumnWidth(1),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1),
      },
      border: pw.TableBorder.all(),
      children: [
        pw.TableRow(
          children: [
            baseText(
              appContext.tr.date,
              arabicFont: arabicFont,
              isBold: true,
            ),
            baseText(
              appContext.tr.kpi,
              arabicFont: arabicFont,
              isBold: true,
            ),
            baseText(
              appContext.tr.status,
              arabicFont: arabicFont,
              isBold: true,
            ),
          ],
        ),
        ...tasksByDate.entries.map(
          (entry) {
            int closedTasksCount =
                entry.value.where((task) => task.isCompleted).length;
            double closedTasksPercentage =
                (closedTasksCount / entry.value.length) * 100;

            String status = closedTasksPercentage > 85
                ? appContext.tr.approved
                : appContext.tr.notApproved;

            return pw.TableRow(
              children: [
                baseText(
                  entry.key.formatDateToString,
                  arabicFont: arabicFont,
                ),
                baseText(
                  '${closedTasksPercentage.isNaN ? 0 : closedTasksPercentage.toStringAsFixed(0)}%',
                  arabicFont: arabicFont,
                ),
                baseText(
                  status,
                  arabicFont: arabicFont,
                  color: closedTasksPercentage > 85
                      ? PdfColors.green
                      : PdfColors.red,
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  pdf.addPage(
    pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      build: (pw.Context context) {
        return <pw.Widget>[
          pw.Header(
            level: 0,
            child: pw.Text(
              '${employee.name} (${fromDate.formatDateToString} - ${toDate.formatDateToString})',
              textScaleFactor: 2,
              textAlign: pw.TextAlign.center,
              textDirection: pw.TextDirection.rtl,
              style: pw.TextStyle(
                font: arabicFont,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
          ),

          //? Total of kpi and status
          pw.Builder(
            builder: (pw.Context context) {
              int closedTasksCount =
                  tasks.where((task) => task.isCompleted).length;
              double closedTasksPercentage =
                  (closedTasksCount / tasks.length) * 100;

              String status = closedTasksPercentage > 85
                  ? appContext.tr.approved
                  : appContext.tr.notApproved;

              return pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    '${appContext.tr.kpi}: ${closedTasksPercentage.isNaN ? 0 : closedTasksPercentage.toStringAsFixed(0)}%',
                    textScaleFactor: 1.5,
                    textAlign: pw.TextAlign.center,
                    textDirection: pw.TextDirection.rtl,
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.Text(
                    '${appContext.tr.status}: $status',
                    textScaleFactor: 1.5,
                    textAlign: pw.TextAlign.center,
                    textDirection: pw.TextDirection.rtl,
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontWeight: pw.FontWeight.bold,
                      color: closedTasksPercentage > 85
                          ? PdfColors.green
                          : PdfColors.red,
                    ),
                  ),
                ],
              );
            },
          ),

          pw.SizedBox(height: 20),

          arabicTable()
        ];
      },
    ),
  );

  return pdf;
}
