import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:opti4t_tasks/src/core/consts/app_constants.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/screens/attendance/models/attendance_model.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/reports/repositories/report_repository_helper.dart';
import 'package:opti4t_tasks/src/screens/vacations/models/vacation_model.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/src/widgets/font.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:xr_helper/xr_helper.dart';

import 'attendance_report_helper.dart';

Future<pw.Document> generateAllEmployeeAttendanceReportPDF(
  BuildContext appContext, {
  required UserModel employee,
  required List<UserModel?> allEmployees,
  required List<AttendanceModel> attendances,
  required List<VacationModel> allVacations,
  required DateTime fromDate,
  required DateTime toDate,
}) async {
  final arabicFont = Font.ttf(
    await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
  );

  final pdf = pw.Document();

  final isEnglish = appContext.isAppEnglish;

  bool isSaturdayAndSomeUserIdsAreInVacation(VacationModel vacation,
      DateTime? currentTableAttendanceDate, String? uid) {
    final usersIds = AppConsts.userIdsInVacationInSaturday;

    if (currentTableAttendanceDate!.weekday == DateTime.saturday &&
        usersIds.contains(uid)) {
      return true;
    }
    return false;
  }

  List<pw.TableRow> generateAttendanceRows({
    bool isArabic = false,
    required DateTime? currentTableAttendanceDate,
  }) {
    var rows = <pw.TableRow>[];

    for (var emp in allEmployees) {
      var attendance = attendances.firstWhereOrNull((att) =>
          att.uid == emp?.uid && att.date == currentTableAttendanceDate);
      var vacationsForEmployee = allVacations
          .where((vacation) =>
              vacation.uid == emp?.uid &&
                  vacation.isApproved == true &&
                  vacation.fromDate != null &&
                  vacation.toDate != null &&
                  (vacation.fromDate!.isBefore(currentTableAttendanceDate!) ||
                      vacation.fromDate!
                          .isSameDay(currentTableAttendanceDate)) &&
                  (vacation.toDate!.isAfter(currentTableAttendanceDate) ||
                      vacation.toDate!.isSameDay(currentTableAttendanceDate)) ||
              isSaturdayAndSomeUserIdsAreInVacation(
                  vacation, currentTableAttendanceDate, emp?.uid))
          .toList();

      final isOnVacation = vacationsForEmployee.isNotEmpty;

      final checkIns = isOnVacation
          ? (isArabic ? 'إجازة' : 'vacation')
          : attendance?.checkedInAt.join('\n') ??
              (isArabic ? 'غائب' : 'absent');

      final partCheckOuts = isOnVacation
          ? '-'
          : attendance?.partCheckoutReason.entries
                  .map((e) => e.key)
                  .join('\n') ??
              '-';
      final partCheckOutsReasons = isOnVacation
          ? '-'
          : attendance?.partCheckoutReason.entries
                  .map((e) => e.value)
                  .join('\n') ??
              '-';
      final checkOut = isOnVacation ? '-' : attendance?.checkedOutAt ?? '-';

      final totalAttendanceTime = isOnVacation
          ? '-'
          : attendance != null
              ? formatDuration(
                  calculateTotalAttendanceTime(attendance), isArabic)
              : '-';

      final totalPartialTimes = isOnVacation
          ? '-'
          : attendance != null
              ? formatDuration(calculateTotalPartialTimes(attendance), isArabic)
              : '-';

      final isFriday = currentTableAttendanceDate?.weekday == DateTime.friday;

      final decorationColor = isFriday
          ? PdfColors.yellow50
          : isOnVacation
              ? PdfColors.amber50
              : attendance == null
                  ? PdfColors.red50
                  : null;
      final textColor = PdfColors.black;

      if (isArabic) {
        rows.add(pw.TableRow(
          decoration: pw.BoxDecoration(
            color: decorationColor,
          ),
          children: [
            baseText(totalAttendanceTime,
                arabicFont: arabicFont, color: textColor),
            baseText(checkOut, arabicFont: arabicFont, color: textColor),
            baseText(totalPartialTimes,
                arabicFont: arabicFont, color: textColor),
            baseText(partCheckOutsReasons,
                arabicFont: arabicFont, color: textColor),
            baseText(partCheckOuts, arabicFont: arabicFont, color: textColor),
            baseText(checkIns, arabicFont: arabicFont, color: textColor),
            baseText(emp?.name ?? emp?.email ?? '-',
                arabicFont: arabicFont, color: textColor),
          ],
        ));
      } else {
        rows.add(pw.TableRow(
          decoration: pw.BoxDecoration(
            color: decorationColor,
          ),
          children: [
            baseText(emp?.name ?? emp?.email ?? '-',
                arabicFont: arabicFont, color: textColor),
            baseText(checkIns, arabicFont: arabicFont, color: textColor),
            baseText(partCheckOuts, arabicFont: arabicFont, color: textColor),
            baseText(partCheckOutsReasons,
                arabicFont: arabicFont, color: textColor),
            baseText(totalPartialTimes,
                arabicFont: arabicFont, color: textColor),
            baseText(checkOut, arabicFont: arabicFont, color: textColor),
            baseText(totalAttendanceTime,
                arabicFont: arabicFont, color: textColor),
          ],
        ));
      }
    }

    return rows;
  }

  pw.Widget englishTable(List<UserModel?> allEmployees,
      List<AttendanceModel> attendances, DateTime? currentTableAttendanceDate) {
    List<pw.TableRow> headerRow = [
      pw.TableRow(
        children: [
          baseText('Employee', arabicFont: arabicFont, isBold: true),
          baseText('Check-Ins', arabicFont: arabicFont, isBold: true),
          baseText('Partial Times', arabicFont: arabicFont, isBold: true),
          baseText('Partial Reasons', arabicFont: arabicFont, isBold: true),
          baseText('Total Partial Times', arabicFont: arabicFont, isBold: true),
          baseText('Check-Out', arabicFont: arabicFont, isBold: true),
          baseText('Total Attendance Time',
              arabicFont: arabicFont, isBold: true),
        ],
      ),
    ];
    List<pw.TableRow> attendanceRows = generateAttendanceRows(
        currentTableAttendanceDate: currentTableAttendanceDate);
    return pw.Table(
      columnWidths: {
        0: const pw.FlexColumnWidth(3),
        1: const pw.FlexColumnWidth(2),
        2: const pw.FlexColumnWidth(2),
        3: const pw.FlexColumnWidth(3),
        4: const pw.FlexColumnWidth(3),
        5: const pw.FlexColumnWidth(2),
        6: const pw.FlexColumnWidth(3),
      },
      border: pw.TableBorder.all(),
      children: headerRow + attendanceRows,
    );
  }

  pw.Widget arabicTable(List<UserModel?> allEmployees,
      List<AttendanceModel> attendances, DateTime? currentTableAttendanceDate) {
    List<pw.TableRow> headerRow = [
      pw.TableRow(
        children: [
          baseText('إجمالي وقت الحضور', arabicFont: arabicFont, isBold: true),
          baseText('وقت الخروج', arabicFont: arabicFont, isBold: true),
          baseText('إجمالي أوقات الخروج الجزئي',
              arabicFont: arabicFont, isBold: true),
          baseText('سبب الخروج الجزئي', arabicFont: arabicFont, isBold: true),
          baseText('وقت الخروج الجزئي', arabicFont: arabicFont, isBold: true),
          baseText('وقت الدخول', arabicFont: arabicFont, isBold: true),
          baseText('الموظف', arabicFont: arabicFont, isBold: true),
        ],
      ),
    ];
    List<pw.TableRow> attendanceRows = generateAttendanceRows(
        isArabic: true, currentTableAttendanceDate: currentTableAttendanceDate);
    return pw.Table(
      columnWidths: {
        0: const pw.FlexColumnWidth(3),
        1: const pw.FlexColumnWidth(2),
        2: const pw.FlexColumnWidth(3),
        3: const pw.FlexColumnWidth(3),
        4: const pw.FlexColumnWidth(2),
        5: const pw.FlexColumnWidth(2),
        6: const pw.FlexColumnWidth(3),
      },
      defaultVerticalAlignment: pw.TableCellVerticalAlignment.middle,
      border: pw.TableBorder.all(),
      children: headerRow + attendanceRows,
    );
  }

  List<pw.Widget> generateAttendanceTables(
      List<UserModel?> allEmployees, List<AttendanceModel> attendances,
      {required pw.Font arabicFont, bool isArabic = false}) {
    var tables = <pw.Widget>[];

    var groupedAttendances =
        groupBy(attendances, (AttendanceModel att) => att.date);

    for (var date in groupedAttendances.keys) {
      var dateAttendances = groupedAttendances[date] ?? [];

      tables.add(pw.Header(
        level: 1,
        child: pw.Align(
          alignment:
              isArabic ? pw.Alignment.centerRight : pw.Alignment.centerLeft,
          child: pw.Text(
            '${date?.formatDateToString ?? '-'} - ${isEnglish ? date.dayNameEn : date.dayNameAr}',
            textScaleFactor: 1.5,
            textAlign: isArabic ? pw.TextAlign.right : pw.TextAlign.left,
            textDirection:
                isArabic ? pw.TextDirection.rtl : pw.TextDirection.ltr,
            style: pw.TextStyle(
              font: arabicFont,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ),
      ));

      tables.add(isEnglish
          ? englishTable(allEmployees, dateAttendances, date)
          : arabicTable(allEmployees, dateAttendances, date));
    }

    return tables;
  }

  final filteredAllEmployees = allEmployees
      .where(
        (element) => element?.isActive == true,
      )
      .toList();

  pdf.addPage(
    pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      build: (pw.Context context) {
        return <pw.Widget>[
          pw.Header(
            level: 0,
            child: pw.Text(
              '${employee.name} (${fromDate.formatDateToString} - ${toDate.formatDateToString})',
              textScaleFactor: 2,
              textAlign: pw.TextAlign.center,
              textDirection: pw.TextDirection.rtl,
              style: pw.TextStyle(
                font: arabicFont,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
          ),
          ...generateAttendanceTables(filteredAllEmployees, attendances,
              arabicFont: arabicFont, isArabic: !isEnglish),
        ];
      },
    ),
  );

  return pdf;
}
