import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/tasks/models/task_model.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/src/widgets/font.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:xr_helper/xr_helper.dart';

import '../report_repository_helper.dart';

Future<pw.Document> generateTasksReportPDF(BuildContext appContext,
    {required UserModel employee,
    required List<TaskModel> tasks,
    required DateTime fromDate,
    required DateTime toDate}) async {
  final arabicFont = Font.ttf(
    await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
  );

  final pdf = pw.Document();

  final isEnglish = appContext.isAppEnglish;

  List<pw.TableRow> generateTaskRows(List<TaskModel> tasks,
      {required pw.Font arabicFont, bool isArabic = false}) {
    var rows = <pw.TableRow>[];
    var processedSubtasks = <String>{};

    for (var task in tasks.where((t) => t.isParentTask == true)) {
      var subtasks = tasks.where((t) => t.parentId == task.id).toList();

      String description = task.description;
      String closedAt =
          task.closedAt?.toDate().formatDateToStringWithTime ?? '-';

      if (description.isEmpty && subtasks.isNotEmpty) {
        description = subtasks.first.description;
        closedAt =
            subtasks.first.closedAt?.toDate().formatDateToStringWithTime ?? '-';
        processedSubtasks.add(subtasks.first.id!);
      }

      if (isArabic) {
        rows.add(pw.TableRow(
          children: [
            baseText(description, arabicFont: arabicFont),
            baseText('$closedAt\n${task.closedAt?.toDate().dayNameEn}',
                arabicFont: arabicFont, isLtr: true),
            baseText(
                '${task.createdAt?.toDate().formatDateToStringWithTime ?? '-'}\n${task.createdAt?.toDate().dayNameEn}',
                arabicFont: arabicFont,
                isLtr: true),
            baseText(task.project, arabicFont: arabicFont),
          ],
        ));
      } else {
        rows.add(pw.TableRow(
          children: [
            baseText(task.project, arabicFont: arabicFont),
            baseText(
                '${task.createdAt?.toDate().formatDateToStringWithTime ?? '-'}\n${task.createdAt?.toDate().dayNameEn}',
                arabicFont: arabicFont,
                isLtr: true),
            baseText('$closedAt\n${task.closedAt?.toDate().dayNameEn}',
                arabicFont: arabicFont, isLtr: true),
            baseText(description, arabicFont: arabicFont),
          ],
        ));
      }

      for (var subtask
          in subtasks.where((st) => !processedSubtasks.contains(st.id))) {
        rows.add(pw.TableRow(
          children: [
            baseText(subtask.description, arabicFont: arabicFont),
            baseText(
                '${subtask.closedAt?.toDate().formatDateToStringWithTime ?? '-'}\n${subtask.closedAt?.toDate().dayNameEn}',
                arabicFont: arabicFont,
                isLtr: true),
            baseText(
              '${subtask.createdAt?.toDate().formatDateToStringWithTime ?? '-'}\n${subtask.createdAt?.toDate().dayNameEn}',
              arabicFont: arabicFont,
              isLtr: true,
            ),
            baseText(subtask.project, arabicFont: arabicFont),
          ],
        ));
      }
    }

    return rows;
  }

  englishTable() {
    List<pw.TableRow> headerRow = [
      pw.TableRow(
        children: [
          baseText(appContext.tr.project, arabicFont: arabicFont, isBold: true),
          baseText(appContext.tr.startDate,
              arabicFont: arabicFont, isBold: true),
          baseText(appContext.tr.endDate, arabicFont: arabicFont, isBold: true),
          baseText(appContext.tr.description,
              arabicFont: arabicFont, isBold: true),
        ],
      ),
    ];
    List<pw.TableRow> taskRows =
        generateTaskRows(tasks, arabicFont: arabicFont);
    return pw.Table(
      columnWidths: {
        0: const pw.FlexColumnWidth(1),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1),
        3: const pw.FlexColumnWidth(2),
      },
      border: pw.TableBorder.all(),
      children: headerRow + taskRows,
    );
  }

  arabicTable() {
    // Define the header row with titles in Arabic

    List<pw.TableRow> headerRow = [
      pw.TableRow(
        children: [
          baseText(appContext.tr.description,
              arabicFont: arabicFont, isBold: true),
          baseText(appContext.tr.endDate, arabicFont: arabicFont, isBold: true),
          baseText(appContext.tr.startDate,
              arabicFont: arabicFont, isBold: true),
          baseText(appContext.tr.project, arabicFont: arabicFont, isBold: true),
        ],
      ),
    ];

    // Generate task rows, including logic for parent and subtask descriptions
    List<pw.TableRow> taskRows =
        generateTaskRows(tasks, arabicFont: arabicFont, isArabic: true);

    // Combine header and task rows to form the complete table
    return pw.Table(
      columnWidths: {
        0: const pw.FlexColumnWidth(2),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1),
        3: const pw.FlexColumnWidth(1),
      },
      border: pw.TableBorder.all(),
      children: headerRow + taskRows,
    );
  }

  pdf.addPage(
    pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      build: (pw.Context context) {
        return <pw.Widget>[
          pw.Header(
            level: 0,
            child: pw.Text(
              '${employee.name} (${fromDate.formatDateToString} - ${toDate.formatDateToString})',
              textScaleFactor: 2,
              textAlign: pw.TextAlign.center,
              textDirection: pw.TextDirection.rtl,
              style: pw.TextStyle(
                font: arabicFont,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
          ),
          if (isEnglish) englishTable() else arabicTable()
        ];
      },
    ),
  );

  return pdf;
}
