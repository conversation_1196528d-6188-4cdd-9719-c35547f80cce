import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:opti4t_tasks/src/core/consts/app_constants.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/reports/repositories/report_repository_helper.dart';
import 'package:opti4t_tasks/src/screens/vacations/models/vacation_model.dart';
import 'package:pdf/src/widgets/font.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:xr_helper/xr_helper.dart';

Future<pw.Document> generateVacationReportPDF(BuildContext appContext,
    {required UserModel employee,
    required List<VacationModel> vacationsList,
    required String selectedVacationStatus,
    required DateTime fromDate,
    required DateTime toDate}) async {
  final isAllEmployees = employee.uid == AppConsts.allEmployees;

  final isAll = selectedVacationStatus == appContext.tr.all;
  final isApproved = selectedVacationStatus == appContext.tr.approved;
  final isRejected = selectedVacationStatus == appContext.tr.rejected;

  final filteredVacations = isAllEmployees
      ? vacationsList
      : vacationsList.where((vacation) {
          return vacation.uid == employee.uid;
        }).toList();

  final arabicFont = Font.ttf(
    await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
  );

  final pdf = pw.Document();

  final isEnglish = appContext.isAppEnglish;

  englishTable(pw.Context context) {
    return pw.Table(
      border: pw.TableBorder.all(),
      children: [
        pw.TableRow(
          children: [
            baseText(appContext.tr.employee,
                arabicFont: arabicFont, isBold: true),
            baseText(appContext.tr.reason,
                arabicFont: arabicFont, isBold: true),
            baseText(appContext.tr.fromDate,
                arabicFont: arabicFont, isBold: true),
            baseText(appContext.tr.toDate,
                arabicFont: arabicFont, isBold: true),
            baseText(
              appContext.tr.days,
              arabicFont: arabicFont,
              isBold: true,
            ),
            if (isAll)
              baseText(appContext.tr.isApproved,
                  arabicFont: arabicFont, isBold: true),
          ],
        ),
        ...filteredVacations.map(
          (vacation) {
            final days = (vacation.toDate
                        ?.difference(vacation.fromDate ??
                            vacation.createdAt?.toDate() ??
                            DateTime.now())
                        .inDays ??
                    0) +
                1;
            return pw.TableRow(
              children: [
                baseText(vacation.userName, arabicFont: arabicFont),
                baseText(vacation.reason.isEmpty ? '-' : vacation.reason,
                    arabicFont: arabicFont),
                baseText(vacation.fromDate.formatDateToString,
                    arabicFont: arabicFont),
                baseText(vacation.toDate.formatDateToString,
                    arabicFont: arabicFont),
                baseText(days.toString(), arabicFont: arabicFont),
                if (isAll)
                  baseText(
                      vacation.isApproved == true
                          ? appContext.tr.approved
                          : appContext.tr.notApproved,
                      arabicFont: arabicFont),
              ],
            );
          },
        ),
      ],
    );
  }

  arabicTable(pw.Context context) {
    return pw.Table(
      border: pw.TableBorder.all(),
      children: [
        pw.TableRow(
          children: [
            baseText(
              appContext.tr.days,
              arabicFont: arabicFont,
              isBold: true,
            ),
            if (isAll)
              baseText(appContext.tr.isApproved,
                  arabicFont: arabicFont, isBold: true),
            baseText(appContext.tr.toDate,
                arabicFont: arabicFont, isBold: true),
            baseText(appContext.tr.fromDate,
                arabicFont: arabicFont, isBold: true),
            baseText(appContext.tr.reason,
                arabicFont: arabicFont, isBold: true),
            baseText(appContext.tr.employee,
                arabicFont: arabicFont, isBold: true),
          ],
        ),
        ...filteredVacations
            .map((vacation) {
              final days = (vacation.toDate
                          ?.difference(vacation.fromDate ??
                              vacation.createdAt?.toDate() ??
                              DateTime.now())
                          .inDays ??
                      0) +
                  1;

              return pw.TableRow(
                children: [
                  // days
                  baseText(days.toString(), arabicFont: arabicFont),
                  if (isAll)
                    baseText(
                        vacation.isApproved == true
                            ? appContext.tr.approved
                            : appContext.tr.notApproved,
                        arabicFont: arabicFont),
                  baseText(vacation.toDate.formatDateToString,
                      arabicFont: arabicFont),
                  baseText(vacation.fromDate.formatDateToString,
                      arabicFont: arabicFont),
                  baseText(vacation.reason.isEmpty ? '-' : vacation.reason,
                      arabicFont: arabicFont),
                  baseText(vacation.userName, arabicFont: arabicFont),
                ],
              );
            })
            .toList()
            .reversed,
      ],
    );
  }

  final title = isAll
      ? appContext.tr.allVacationsReport
      : isApproved
          ? appContext.tr.approvedVacationsReport
          : appContext.tr.rejectedVacationsReport;

  final allVacationDays = filteredVacations.fold<int>(
    0,
    (previousValue, vacation) {
      final days = (vacation.toDate
                  ?.difference(vacation.fromDate ??
                      vacation.createdAt?.toDate() ??
                      DateTime.now())
                  .inDays ??
              0) +
          1;
      return previousValue + days;
    },
  );

  pdf.addPage(
    pw.Page(
      build: (context) => pw.Column(
        children: [
          baseText(
            '$title (${fromDate.formatDateToString} - ${toDate.formatDateToString})\n${appContext.tr.totalDays}: $allVacationDays',
            arabicFont: arabicFont,
            isBold: true,
          ),
          pw.SizedBox(height: 20),
          isEnglish ? englishTable(context) : arabicTable(context),
        ],
      ),
    ),
  );

  return pdf;
}
