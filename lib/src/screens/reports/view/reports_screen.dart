import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/consts/app_constants.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/home/<USER>/widgets/home_top_section.dart';
import 'package:opti4t_tasks/src/screens/reports/view/widgets/report_buttons.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/report_fields.dart';

class ReportsScreen extends HookConsumerWidget {
  const ReportsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedEmployee = useState<UserModel?>(UserModel(
      uid: AppConsts.allEmployees,
      name: context.tr.allEmployees,
      email: '-',
    ));

    final selectedFromDate = useState<DateTime>(
        DateTime(DateTime.now().year, DateTime.now().month, 1));

    final selectedToDate = useState<DateTime>(DateTime.now());

    final selectedDates = (selectedFromDate, selectedToDate);

    final generatedReport = useState<String>('');

    final selectedReportType = useState<String>(context.tr.attendanceReport);

    final selectedVacationStatus = useState<String>(context.tr.all);

    return Scaffold(
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const HomeTopSection(),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // * Report Fields
                ReportFields(
                  selectedEmployee: selectedEmployee,
                  selectedDates: selectedDates,
                  selectedReportType: selectedReportType,
                  selectedVacationStatus: selectedVacationStatus,
                ),

                context.largeGap,

                // * Generate Report Button & Open Report & Send Email Button
                ReportButtons(
                  selectedEmployee: selectedEmployee,
                  selectedDates: selectedDates,
                  generatedReport: generatedReport,
                  selectedReportType: selectedReportType,
                  selectedVacationStatus: selectedVacationStatus,
                )
              ],
            ).paddingAll(AppSpaces.largePadding - 2),
          ],
        ),
      ),
    );
  }
}
