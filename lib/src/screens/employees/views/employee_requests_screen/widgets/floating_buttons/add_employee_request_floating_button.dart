import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/data/remote/api_consts.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/services/media/controller/media_controller.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/drop_downs/employees_drop_down.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/shared_widgets.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/clients/models/client_model.dart';
import 'package:opti4t_tasks/src/screens/employees/models/employee_request_model.dart';
import 'package:opti4t_tasks/src/screens/employees/providers/employee_requests_providers.dart';
import 'package:opti4t_tasks/src/screens/employees/views/employee_requests_screen/widgets/floating_buttons/close_all_employee_request_floating_button.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../core/shared_widgets/drop_downs/clients_drop_down.dart';
import '../../../../../clients/providers/clients_providers.dart';

class AddEmployeeRequestFloatingButton extends ConsumerWidget {
  const AddEmployeeRequestFloatingButton({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaController = ref.watch(mediaPickerControllerProvider);

    // final canAddEmployeeRequests =
    //     UserModelHelper.canViewAllTasks() || UserModelHelper.isManager();
    //
    // if (!canAddEmployeeRequests) {
    //   return const SizedBox();
    // }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (UserModelHelper.isEmployee())
          CloseAllEmployeeRequestsFloatingButton().paddingOnly(
            bottom: AppSpaces.mediumPadding,
          ),
        FloatingActionButton(
          onPressed: () => showDialog(
                  context: context, builder: (_) => AddEmployeeRequestDialog())
              .then((value) => mediaController.clearFiles()),
          child: const Icon(Icons.add),
        ),
      ],
    );
  }
}

class AddEmployeeRequestDialog extends HookConsumerWidget {
  final EmployeeRequestModel? employeeRequest; //? For Edit

  const AddEmployeeRequestDialog({
    super.key,
    this.employeeRequest,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final employeeRequestController =
        ref.watch(employeeRequestControllerNotifierProvider(context));
    final clientController =
        ref.watch(clientControllerNotifierProvider(context));

    final mediaController = ref.watch(mediaPickerControllerProvider);

    final controllers = {
      // ApiConsts.clientName:
      //     useTextEditingController(text: employeeRequest?.clientName),
      ApiConsts.description:
          useTextEditingController(text: employeeRequest?.description),
      ApiConsts.workTime:
          useTextEditingController(text: employeeRequest?.workTime),
    };

    final valueNotifiers = {
      ApiConsts.formKey: useState(GlobalKey<FormState>()),
      ApiConsts.priority: useState(employeeRequest?.priority ?? "Low"),
      ApiConsts.employee: useState<UserModel?>(null),
      ApiConsts.date:
          useState<DateTime>(employeeRequest?.date ?? DateTime.now()),
      ApiConsts.endDate: useState<DateTime?>(employeeRequest?.endDate),
      ApiConsts.client: useState<ClientRequestModel?>(null),
      ApiConsts.project: useState<ClientRequestModel?>(null),
    };

    final nameFocusNode = useFocusNode();

    final isEdit = employeeRequest != null;

    final title = isEdit
        ? context.tr.editEmployeeRequest
        : context.tr.addNewEmployeeRequest;

    useEffect(() {
      nameFocusNode.requestFocus();

      return () {};
    }, []);

    final selectedEmployee =
        valueNotifiers[ApiConsts.employee]! as ValueNotifier<UserModel?>;
    final formKey = valueNotifiers[ApiConsts.formKey]!
        as ValueNotifier<GlobalKey<FormState>>;
    final date = valueNotifiers[ApiConsts.date]! as ValueNotifier<DateTime>;
    final endDate =
        valueNotifiers[ApiConsts.endDate]! as ValueNotifier<DateTime?>;
    final priority =
        valueNotifiers[ApiConsts.priority]! as ValueNotifier<String?>;
    // final clientNameController = controllers[ApiConsts.clientName]!;
    final selectedClient =
        valueNotifiers[ApiConsts.client]! as ValueNotifier<ClientRequestModel?>;
    final selectedProject = valueNotifiers[ApiConsts.project]!
        as ValueNotifier<ClientRequestModel?>;
    final descriptionController = controllers[ApiConsts.description]!;
    final workTimeController = controllers[ApiConsts.workTime]!;

    void validateAndAddOrEditEmployeeRequest() async {
      if (formKey.value.currentState!.validate()) {
        if (isEdit) {
          final copiedEmployeeRequest = employeeRequest!.copyWith(
            projectId: selectedProject.value?.id ?? '',
            projectName: selectedProject.value?.name ?? '',
            clientId: selectedClient.value?.id ?? '',
            clientName: selectedClient.value?.name ?? '',
            description: descriptionController.text,
            workTime: workTimeController.text,
            date: date.value,
            priority: priority.value ?? 'Low',
            uid: selectedEmployee.value?.uid ?? '',
            userEmail: selectedEmployee.value?.email ?? '',
            userName: selectedEmployee.value?.name ?? '',
          );

          await employeeRequestController.editEmployeeRequest(
            employeeRequest: copiedEmployeeRequest,
            filePath: mediaController.filePath,
          );
        } else {
          if (selectedEmployee.value == null && UserModelHelper.isManager()) {
            context.showBarMessage(context.tr.pleaseSelectEmployee,
                isError: true);
            return;
          }

          final employeeRequest = EmployeeRequestModel(
            projectId: selectedProject.value?.id ?? '',
            projectName: selectedProject.value?.name ?? '',
            clientId: selectedClient.value?.id ?? '',
            clientName: selectedClient.value?.name ?? '',
            description: descriptionController.text,
            workTime: workTimeController.text,
            date: date.value,
            endDate: endDate.value,
            priority: priority.value ?? 'Low',
            uid: selectedEmployee.value?.uid ??
                UserModelHelper.signedUser().uid ??
                '',
            userEmail: selectedEmployee.value?.email ??
                UserModelHelper.signedUser().email,
            userName: selectedEmployee.value?.name ??
                UserModelHelper.signedUser().name,
            createdAt: Timestamp.fromDate(DateTime.now()),
          );

          await employeeRequestController.addEmployeeRequest(
            employeeRequest: employeeRequest,
            filePath: mediaController.filePath,
          );

          final client = ClientRequestModel(
            parentId: selectedProject.value?.id ?? '',
            description: descriptionController.text,
            selectedEmployees: UserModelHelper.isManager()
                ? [selectedEmployee.value?.uid ?? '']
                : [UserModelHelper.localUID()],
            uid: selectedEmployee.value?.uid ?? '',
            userEmail: selectedEmployee.value?.email ??
                UserModelHelper.signedUser().email ??
                '',
            userName: selectedEmployee.value?.name ??
                UserModelHelper.signedUser().name ??
                '',
            createdAt: Timestamp.fromDate(DateTime.now()),
            date: DateTime.now(),
            isParentClient: false,
            clientName: selectedClient.value?.name ?? '',
            clientId: selectedClient.value?.id ?? '',
          );

          await clientController.addClient(
            client: client,
            filePath: mediaController.filePath,
            isProject: true,
          );
        }
      }
    }

    if (employeeRequestController.isLoading) {
      return const Center(
        child: LoadingWidget(),
      );
    }

    return Dialog(
        child: Form(
      key: formKey.value,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpaces.largePadding - 4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const CircleAvatar(
                  backgroundColor: ColorManager.primaryColor,
                  child: Icon(
                    Icons.notes,
                    color: Colors.white,
                  ),
                ),
                context.mediumGap,
                Text(
                  title,
                  style: context.whiteTitle,
                ),
              ],
            ),
            context.largeGap,
            _AddRequestFields(
              employeeRequest: employeeRequest,
              nameFocusNode: nameFocusNode,
              controllers: controllers,
              valueNotifiers: valueNotifiers,
            ),
            context.largeGap,
            Button(
              label: isEdit ? context.tr.edit : context.tr.submit,
              onPressed: validateAndAddOrEditEmployeeRequest,
              textColor: Colors.white,
            )
          ],
        ),
      ),
    ));
  }
}

class _AddRequestFields extends StatelessWidget {
  final FocusNode nameFocusNode;
  final EmployeeRequestModel? employeeRequest;
  final Map<String, TextEditingController> controllers;
  final Map<String, ValueNotifier> valueNotifiers;

  const _AddRequestFields({
    required this.nameFocusNode,
    required this.employeeRequest,
    required this.controllers,
    required this.valueNotifiers,
  });

  @override
  Widget build(BuildContext context) {
    final selectedEmployee =
        valueNotifiers[ApiConsts.employee]! as ValueNotifier<UserModel?>;
    // final clientNameController = controllers[ApiConsts.clientName]!;
    final descriptionController = controllers[ApiConsts.description]!;

    return Column(
      children: [
        if (UserModelHelper.isManager())
          EmployeesDropDown(
            selectedEmployee: selectedEmployee,
            label: context.tr.assignToEmployee,
            employeeId: employeeRequest?.uid,
          ),

        context.largeGap,

        //! Client Drop Down
        ClientsDropDown(
          selectedClient: valueNotifiers[ApiConsts.client]!
              as ValueNotifier<ClientRequestModel?>,
          label: context.tr.client,
          clientId: employeeRequest?.clientId,
        ),

        context.largeGap,

        //! Projects Drop Down
        ClientsDropDown(
          selectedClient: valueNotifiers[ApiConsts.project]!
              as ValueNotifier<ClientRequestModel?>,
          label: context.tr.project,
          clientId: employeeRequest?.clientId,
          isProjects: true,
        ),
        // BaseTextField(
        //   label: context.tr.clientName,
        //   focusNode: nameFocusNode,
        //   onChanged: (_) {},
        //   isWhiteText: true,
        //   controller: clientNameController,
        //   validator: (value) {
        //     return Validations.mustBeNotEmpty(value,
        //         emptyMessage: context.tr.fieldCannotBeEmpty);
        //   },
        // ),

        context.largeGap,

        //! Description
        BaseTextField(
          label: context.tr.description,
          isWhiteText: true,
          controller: descriptionController,
          minLines: 1,
          maxLines: 5,
          textInputType: TextInputType.multiline,
          validator: (value) {
            return Validations.mustBeNotEmpty(value,
                emptyMessage: context.tr.fieldCannotBeEmpty);
          },
        ),

        context.largeGap,

        // drop down for priority (low - medium - high)
        BaseDropDown(
          label: context.tr.priority,
          selectedValue: valueNotifiers[ApiConsts.priority]!.value,
          isWhiteText: true,
          asString: (p0) {
            if (p0 == 'Low') {
              return context.tr.low;
            } else if (p0 == 'Medium') {
              return context.tr.medium;
            } else {
              return context.tr.high;
            }
          },
          data: [
            "Low",
            "Medium",
            "High",
          ],
          onChanged: (value) {
            if (value == context.tr.low) {
              valueNotifiers[ApiConsts.priority]!.value = 'Low';
            } else if (value == context.tr.medium) {
              valueNotifiers[ApiConsts.priority]!.value = 'Medium';
            } else {
              valueNotifiers[ApiConsts.priority]!.value = 'High';
            }
          },
        ),

        context.largeGap,

        BaseDatePicker(
          selectedDateNotifier:
              valueNotifiers[ApiConsts.date]! as ValueNotifier<DateTime?>,
          label: context.tr.workDate,
          isWhite: true,
          align: context.isAppEnglish
              ? Alignment.centerLeft
              : Alignment.centerRight,
        ),

        context.largeGap,

        BaseDatePicker(
          isRequired: true,
          selectedDateNotifier:
              valueNotifiers[ApiConsts.endDate]! as ValueNotifier<DateTime?>,
          label: context.tr.endDate,
          isWhite: true,
          align: context.isAppEnglish
              ? Alignment.centerLeft
              : Alignment.centerRight,
        ),

        context.largeGap,

        BaseTextField(
          label: context.tr.workTime,
          isWhiteText: true,
          controller: controllers[ApiConsts.workTime]!,
          isRequired: false,
        ),

        context.largeGap,

        SinglePickImageWidget(
          networkImage: employeeRequest?.attachmentUrl,
        ),
      ],
    );
  }
}
