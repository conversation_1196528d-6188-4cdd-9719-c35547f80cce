import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/employees/views/employee_requests_screen/widgets/dialog/close_employee_request_dialog.dart';

class CloseAllEmployeeRequestsFloatingButton extends ConsumerWidget {
  const CloseAllEmployeeRequestsFloatingButton({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final canCloseRequests = UserModelHelper.isEmployee();

    if (!canCloseRequests) {
      return const SizedBox();
    }

    return FloatingActionButton(
      backgroundColor: ColorManager.secondaryColor,
      onPressed: () async {
        showDialog(
            context: context,
            builder: (_) => CloseEmployeeRequestDialog(
                  closeAllRequests: true,
                ));
      },
      child: const Icon(Icons.checklist_rtl),
    );
  }
}
