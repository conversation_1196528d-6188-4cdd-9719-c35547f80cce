import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/employees/models/employee_request_model.dart';
import 'package:opti4t_tasks/src/screens/employees/providers/employee_requests_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class EmployeeRepliesDialog extends HookConsumerWidget {
  final EmployeeRequestModel? request;

  const EmployeeRepliesDialog({
    super.key,
    this.request,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final requestController =
        ref.watch(employeeRequestControllerNotifierProvider(context));

    final reviewController = useTextEditingController();

    final reviewFocusNode = useFocusNode();

    final formKey = useState(GlobalKey<FormState>());

    useEffect(() {
      reviewFocusNode.requestFocus();

      return () {};
    }, []);

    void validateAndAddOrEditTask() async {
      if (formKey.value.currentState!.validate()) {
        final copiedRequest = request!.copyWith(
          replies: request!.replies != null && request!.replies!.isNotEmpty
              ? [...request!.replies!, reviewController.text]
              : [reviewController.text],
        );

        await requestController.editEmployeeRequest(
            employeeRequest: copiedRequest, isReply: true);
        // await taskController.reviewTask(
        //   task: request!,
        //   review: reviewController.text,
        // );
      }
    }

    return Dialog(
        child: Form(
      key: formKey.value,
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const CircleAvatar(
                  backgroundColor: ColorManager.primaryColor,
                  child: Icon(
                    Icons.comment,
                    color: Colors.white,
                  ),
                ),
                context.mediumGap,
                Text(
                  context.tr.replies,
                  style: context.whiteTitle,
                ),
              ],
            ),
            context.largeGap,
            if (request!.replies != null && request!.replies!.isNotEmpty) ...[
              //! List of old replies
              ListView.builder(
                shrinkWrap: true,
                padding: EdgeInsets.zero,
                itemCount: request!.replies!.length,
                itemBuilder: (context, index) {
                  return Text('- ${request!.replies![index]}',
                          style: context.whiteSubTitle)
                      .paddingAll(AppSpaces.smallPadding);
                },
              ),
            ] else
              Center(
                child:
                    Text(context.tr.noRepliesYet, style: context.whiteSubTitle)
                        .paddingAll(AppSpaces.smallPadding),
              ),
            context.largeGap,
            if ((!UserModelHelper.isAdmin() &&
                    request?.uid != UserModelHelper.localUID()) ||
                request?.uid == UserModelHelper.localUID()) ...[
              //! Review
              BaseTextField(
                label: context.tr.reply,
                focusNode: reviewFocusNode,
                isWhiteText: true,
                controller: reviewController,
                maxLines: 3,
                validator: (value) {
                  return Validations.mustBeNotEmpty(value,
                      emptyMessage: context.tr.fieldCannotBeEmpty);
                },
              ),
              context.xLargeGap,
              Button(
                label: context.tr.submit,
                onPressed: validateAndAddOrEditTask,
                textColor: Colors.white,
              )
            ] else
              Button(
                label: context.tr.close,
                onPressed: context.back,
                textColor: Colors.white,
                color: ColorManager.secondaryColor,
              )
          ],
        ),
      ),
    ).paddingAll(AppSpaces.largePadding - 4));
  }
}
