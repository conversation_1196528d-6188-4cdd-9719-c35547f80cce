import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/consts/app_constants.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/employees/models/employee_request_model.dart';
import 'package:opti4t_tasks/src/screens/employees/providers/employee_requests_providers.dart';
import 'package:opti4t_tasks/src/screens/employees/views/employee_requests_screen/widgets/employee_requests_card/employee_request_card.dart';
import 'package:opti4t_tasks/src/screens/employees/views/employee_requests_screen/widgets/filtered_employee_requests/filtered_employee_requests_expansion_tile.dart';
import 'package:xr_helper/xr_helper.dart';

class FilteredEmployeeRequestsList extends HookConsumerWidget {
  const FilteredEmployeeRequestsList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedEmployee = useState<UserModel?>(null);

    final uid = selectedEmployee.value?.uid;

    final params = (context, uid);

    final employeeRequests =
        ref.watch(getEmployeeRequestsStreamControllerProvider(params));

    final requests = employeeRequests.when(
      data: (requests) => requests,
      loading: () => <EmployeeRequestModel>[],
      error: (error, stack) {
        return <EmployeeRequestModel>[];
      },
    );

    final selectedFromDate = useState<DateTime>(
        DateTime(DateTime.now().year, DateTime.now().month, 1));
    final selectedToDate = useState<DateTime>(DateTime.now());
    final requestsByEmployeeRequest = useState<List<EmployeeRequestModel>>([]);
    final selectedType = useState<String>(context.tr.allRequests);

    List<EmployeeRequestModel> filterRequests(
      DateTime fromDate,
      DateTime toDate,
      String requestType,
      UserModel? employee,
    ) {
      return requests.where((request) {
        final createdAt = request.createdAt!.toDate();
        final isWithinDateRange = createdAt.isAfter(fromDate) &&
            createdAt.isBefore(toDate.add(const Duration(days: 1)));
        final matchesEmployee = employee == null ||
            request.uid == employee.uid ||
            employee.uid == AppConsts.allEmployees;
        final matchesType = requestType == context.tr.allRequests ||
            (requestType == context.tr.closedRequests && request.isCompleted) ||
            (requestType == context.tr.openedRequests && !request.isCompleted);

        return isWithinDateRange && matchesEmployee && matchesType;
      }).toList();
    }

    requestsByEmployeeRequest.value = filterRequests(
      selectedFromDate.value,
      selectedToDate.value,
      selectedType.value,
      selectedEmployee.value,
    );

    return Column(
      children: [
        FilterEmployeeRequestsExpansionTile(
          selectedFromDate: selectedFromDate,
          selectedToDate: selectedToDate,
          requestsByEmployeeRequest: requestsByEmployeeRequest,
          selectedType: selectedType,
          selectedEmployee: selectedEmployee,
        ),

        context.smallGap,

        Divider(
          color: ColorManager.secondaryColor.withOpacity(.6),
          thickness: .4,
        ).paddingSymmetric(
          horizontal: AppSpaces.mediumPadding,
        ),

        context.smallGap,

        //! Requests List
        Expanded(
          child: ListView.builder(
            shrinkWrap: true,
            padding: const EdgeInsets.symmetric(
                horizontal: AppSpaces.mediumPadding,
                vertical: AppSpaces.smallPadding),
            itemBuilder: (context, index) {
              final request = requestsByEmployeeRequest.value[index];

              return EmployeeRequestCard(
                employeeRequest: request,
              );
            },
            itemCount: requestsByEmployeeRequest.value.length,
          ),
        ),
      ],
    );
  }
}
