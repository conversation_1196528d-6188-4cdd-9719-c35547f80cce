import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/shared_widgets.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/controllers/auth_controller.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:xr_helper/xr_helper.dart';

class EmployeeDeleteButton extends ConsumerWidget {
  final UserModel user;

  const EmployeeDeleteButton({super.key, required this.user});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const CircleAvatar(
            backgroundColor: ColorManager.errorColor,
            child: Icon(
              Icons.delete,
              color: Colors.white,
            ))
        .onTapWithRipple(() => showDialog(
            context: context,
            builder: (_) => DeleteUserDialog(
                  user: user,
                )))
        .paddingOnly(
            left: context.isAppEnglish ? 10 : 0,
            right: context.isAppEnglish ? 0 : 10);
  }
}

class DeleteUserDialog extends HookConsumerWidget {
  final UserModel user;

  const DeleteUserDialog({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userController = ref.watch(authControllerNotifierProvider(context));

    void deleteUser() async {
      await userController.deleteUser(user: user);
    }

    if (userController.isLoading) {
      return const Center(
        child: LoadingWidget(),
      );
    }

    final title = context.tr.deleteUser;

    final description = context.tr.areYouSureYouWantToDeleteThisUser;

    final buttonLabel = context.tr.delete;

    return Dialog(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const CircleAvatar(
              backgroundColor: ColorManager.primaryColor,
              child: Icon(
                Icons.playlist_add_check_outlined,
                color: Colors.white,
              ),
            ),
            context.mediumGap,
            Text(
              title,
              style: context.whiteTitle,
            ),
          ],
        ),

        context.largeGap,

        //? Are you sure you want to delete this user?
        Text(
          description,
          style: context.whiteSubTitle,
        ),

        context.xLargeGap,

        Row(
          children: [
            //? Cancel Button
            Expanded(
              child: TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text(context.tr.cancel, style: context.whiteSubTitle),
              ),
            ),

            context.mediumGap,

            Expanded(
                flex: 2,
                child: Button(
                  label: buttonLabel,
                  onPressed: deleteUser,
                  textColor: Colors.white,
                )),
          ],
        ).sized(
          height: 45,
        )
      ],
    ).paddingAll(AppSpaces.largePadding - 4));
  }
}
