import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/extensions/riverpod_extensions.dart';
import 'package:opti4t_tasks/src/screens/auth/controllers/auth_controller.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/employees/views/employees_page/widgets/employee_card/employee_card.dart';
import 'package:opti4t_tasks/src/screens/employees/views/employees_page/widgets/empty_employee_widget.dart';
import 'package:xr_helper/xr_helper.dart';

class EmployeesListWidget extends ConsumerWidget {
  const EmployeesListWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final users = ref.watch(getUsersStreamControllerProvider(context));

    return ListView(
      padding: EdgeInsets.zero,
      children: [
        users.get(
          data: (users) {
            if (users.isEmpty) {
              return const EmptyEmployeesWidget();
            }

            final activeUsers = users
                .where(
                  (user) => user.isActive == true && !user.isAdmin,
                )
                .toList();

            final inActiveUsers = users
                .where(
                  (user) => user.isActive == false && !user.isAdmin,
                )
                .toList();

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.tr.activeEmployees,
                  style: context.whiteSubTitle,
                ),
                context.mediumGap,
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.only(
                      bottom: UserModelHelper.isManager()
                          ? 0
                          : AppSpaces.largePadding),
                  itemCount: activeUsers.length,
                  separatorBuilder: (context, index) => context.mediumGap,
                  itemBuilder: (context, index) {
                    final user = activeUsers[index];

                    return EmployeeCard(
                      user: user,
                    );
                  },
                ),
                context.mediumGap,
                if (inActiveUsers.isNotEmpty)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.tr.inactiveEmployees,
                        style: context.whiteSubTitle,
                      ),
                      context.mediumGap,
                      ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.only(
                            bottom: UserModelHelper.isManager()
                                ? 0
                                : AppSpaces.largePadding),
                        itemCount: inActiveUsers.length,
                        separatorBuilder: (context, index) => context.mediumGap,
                        itemBuilder: (context, index) {
                          final user = inActiveUsers[index];

                          return EmployeeCard(
                            user: user,
                          );
                        },
                      ),
                    ],
                  ),
              ],
            );
          },
        ),
      ],
    ).paddingAll(
      AppSpaces.mediumPadding,
    );
  }
}
