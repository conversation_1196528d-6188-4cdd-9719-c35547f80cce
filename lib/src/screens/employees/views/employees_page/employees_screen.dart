import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/employees/views/employee_requests_screen/employee_requests_screen.dart';
import 'package:opti4t_tasks/src/screens/employees/views/employee_requests_screen/widgets/floating_buttons/add_employee_request_floating_button.dart';
import 'package:opti4t_tasks/src/screens/employees/views/employees_page/widgets/employees_list_widget.dart';
import 'package:opti4t_tasks/src/screens/home/<USER>/widgets/home_top_section.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/add_employee_floating_button.dart';

class EmployeesScreen extends HookConsumerWidget {
  const EmployeesScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabController = useTabController(initialLength: 2);
    final selectedTab = useState<int>(0);

    final isEmployeesTab = UserModelHelper.isAdmin() && selectedTab.value == 0;

    final isEmployeeRequestsTab =
        UserModelHelper.isAdmin() && selectedTab.value == 1;

    return Scaffold(
      floatingActionButton: isEmployeesTab
          ? const AddUpdateEmployeeFloatingButton()
          : isEmployeeRequestsTab
              ? AddEmployeeRequestFloatingButton()
              : null,
      body: Column(
        children: [
          context.largeGap,
          HomeTopSection(
            title: context.tr.employees,
          ),
          Expanded(
            child: Column(
              children: [
                TabBar(
                  controller: tabController,
                  onTap: (index) {
                    selectedTab.value = index;
                  },
                  tabs: [
                    Tab(text: context.tr.employees),
                    Tab(text: context.tr.employeesRequests),
                  ],
                ),
                context.mediumGap,
                Expanded(
                  child: TabBarView(
                    controller: tabController,
                    physics: const NeverScrollableScrollPhysics(),
                    children: [
                      //! Employees
                      const EmployeesListWidget(),

                      //! Employee Requests
                      const EmployeeRequestsScreen()
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
