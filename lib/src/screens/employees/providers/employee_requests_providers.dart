import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti4t_tasks/src/screens/employees/controllers/employee_request_controller.dart';
import 'package:opti4t_tasks/src/screens/employees/models/employee_request_model.dart';
import 'package:opti4t_tasks/src/screens/employees/repositories/employee_request_repository.dart';
import 'package:opti4t_tasks/src/screens/tasks/repositories/task_repository.dart';

// * Provider ========================================
final employeeRequestControllerProvider =
    Provider.family<EmployeeRequestController, BuildContext>(
  (ref, context) {
    final employeeRequestRepo = ref.watch(employeeRequestRepoProvider);
    final taskRepo = ref.watch(taskRepoProvider);

    return EmployeeRequestController(
      context,
      employeeRequestRepo: employeeRequestRepo,
      taskRepo: taskRepo,
    );
  },
);

// * Change Notifier Provider ========================================
final employeeRequestControllerNotifierProvider =
    ChangeNotifierProvider.family<EmployeeRequestController, BuildContext>(
  (ref, context) {
    final employeeRequestRepo = ref.watch(employeeRequestRepoProvider);
    final taskRepo = ref.watch(taskRepoProvider);

    return EmployeeRequestController(
      context,
      employeeRequestRepo: employeeRequestRepo,
      taskRepo: taskRepo,
    );
  },
);

// * Get EmployeeRequests Stream Controller ========================================
final getEmployeeRequestsStreamControllerProvider = StreamProvider.family
    .autoDispose<List<EmployeeRequestModel>, (BuildContext, String?)>(
  (ref, params) {
    final context = params.$1;
    final selectedEmployee = params.$2;

    final employeeRequestController = ref.watch(
      employeeRequestControllerProvider(context),
    );

    return employeeRequestController.getEmployeeRequestsStream(
        selectedEmployeeUid: selectedEmployee);
  },
);
