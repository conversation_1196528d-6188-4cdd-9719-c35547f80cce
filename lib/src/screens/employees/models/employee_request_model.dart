import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:xr_helper/xr_helper.dart';

class EmployeeRequestModel extends Equatable {
  final String? id;
  final String uid;
  final String clientName;
  final String clientId;
  final String projectId;
  final String projectName;
  final String userName;
  final String userEmail;
  final String description;
  final String workTime;

  final DateTime? date;
  final DateTime? endDate;
  final Timestamp? createdAt;
  final Timestamp? closedAt;
  final String? attachmentUrl;

  final String? parentId;
  final String? adminEmail;
  final String? adminName;
  final bool? isParentEmployeeRequest;
  final bool? isApproved;
  final List<String>? replies;
  final String priority; // ? 3 types (Low, Medium, High)
  final bool isSuggestion;
  final bool isApprovedSuggestion;

  const EmployeeRequestModel({
    this.id,
    this.clientName = '',
    this.description = '',
    this.workTime = '',
    this.uid = '',
    this.userEmail = '',
    this.clientId = '',
    this.projectId = '',
    this.projectName = '',
    this.userName = '',
    this.priority = 'Low',
    this.date,
    this.endDate,
    this.createdAt,
    this.closedAt,
    this.attachmentUrl,
    this.parentId,
    this.isParentEmployeeRequest,
    this.adminEmail,
    this.adminName,
    this.replies,
    this.isApproved = false,
    this.isApprovedSuggestion = false,
    this.isSuggestion = false,
  });

  @override
  List<Object?> get props => [
        id,
        clientName,
        description,
        date,
        uid,
        userEmail,
        userName,
        createdAt,
        closedAt,
        attachmentUrl,
        workTime,
        parentId,
        isParentEmployeeRequest,
        adminEmail,
        adminName,
        replies,
        isApproved,
        isApprovedSuggestion,
        isSuggestion,
        clientId,
        projectId,
        projectName,
      ];

  bool get isCompleted => closedAt != null;

  String get priorityAR {
    switch (priority) {
      case 'Low':
        return 'منخفضة';
      case 'Medium':
        return 'متوسطة';
      case 'High':
        return 'عالية';
      default:
        return 'منخفضة';
    }
  }

  factory EmployeeRequestModel.fromJson(String id,
      {required Map<String, dynamic> data}) {
    return EmployeeRequestModel(
      id: id,
      clientName: data['client_name'] ?? '',
      uid: data['uid'] ?? '',
      clientId: data['client_id'] ?? '',
      userEmail: data['user_email'] ?? '',
      userName: data['user_name'] ?? '',
      workTime: data['work_time'] ?? '',
      description: data['description'] ?? '',
      date: DateTime.tryParse(data['date'] ?? '') ?? DateTime.now(),
      createdAt: data['createdAt'] as Timestamp?,
      closedAt: data['closedAt'] as Timestamp?,
      attachmentUrl: data['attachment_url'] ?? '',
      parentId: data['parent_id'],
      isApproved: data['is_approved'] ?? false,
      isParentEmployeeRequest: data['is_parent_employeeRequest'],
      adminEmail: data['admin_email'],
      adminName: data['admin_name'],
      replies: List<String>.from(data['replies'] ?? []),
      priority: data['priority'] ?? 'Low',
      endDate: DateTime.tryParse(data['end_date'] ?? ''),
      isSuggestion: data['is_suggestion'] ?? false,
      isApprovedSuggestion: data['is_approved_suggestion'] ?? false,
      projectId: data['project_id'] ?? '',
      projectName: data['project_name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': UserModelHelper.isAdmin() ? uid : UserModelHelper.localUID(),
      'user_email': UserModelHelper.isAdmin()
          ? userEmail
          : UserModelHelper.signedUser().email,
      'user_name': UserModelHelper.isAdmin()
          ? userName
          : UserModelHelper.signedUser().name,
      'work_time': workTime,
      'priority': priority,
      'client_name': clientName,
      'client_id': clientId,
      'description': description,
      'date': date.formatDateToString,
      'end_date': endDate.formatDateToString,
      if (closedAt == null) 'createdAt': createdAt,
      'closedAt': closedAt,
      if (attachmentUrl != null) 'attachment_url': attachmentUrl,
      if (parentId != null) 'parent_id': parentId,
      // 'is_parent_employeeRequest': isParentEmployeeRequest ?? false,
      // 'replies': replies,
      if (UserModelHelper.isAdmin())
        'admin_email': UserModelHelper.signedUser().email,
      if (UserModelHelper.isAdmin())
        'admin_name': UserModelHelper.signedUser().name,
      'is_suggestion': !UserModelHelper.isManager(),
      'is_approved_suggestion': isApprovedSuggestion,
      'project_id': projectId,
      'project_name': projectName,
    };
  }

  //? To Approve Json
  Map<String, dynamic> toJsonApprove() {
    return {
      'is_approved': true,
    };
  }

  //? To Approve Suggestion Json
  Map<String, dynamic> toJsonApproveSuggestion() {
    return {
      'is_approved_suggestion': true,
    };
  }

  //? To reopen json
  Map<String, dynamic> toJsonReopen() {
    return {
      'closedAt': null,
    };
  }

  EmployeeRequestModel copyWith({
    String? id,
    String? clientId,
    String? clientName,
    String? projectId,
    String? projectName,
    String? description,
    bool? isCompleted,
    DateTime? date,
    DateTime? endDate,
    Timestamp? createdAt,
    Timestamp? closedAt,
    String? attachmentUrl,
    String? parentId,
    bool? isParentEmployeeRequest,
    String? uid,
    String? userEmail,
    String? userName,
    String? workTime,
    String? priority,
    List<String>? replies,
    bool? isSuggestion,
    bool? isApprovedSuggestion,
    bool? isApproved,
    String? adminEmail,
    String? adminName,
  }) {
    return EmployeeRequestModel(
      id: id ?? this.id,
      clientId: clientId ?? this.clientId,
      clientName: clientName ?? this.clientName,
      description: description ?? this.description,
      date: date ?? this.date,
      projectId: projectId ?? this.projectId,
      projectName: projectName ?? this.projectName,
      endDate: endDate ?? this.endDate,
      createdAt: createdAt ?? this.createdAt,
      closedAt: closedAt ?? this.closedAt,
      attachmentUrl: attachmentUrl ?? this.attachmentUrl,
      parentId: parentId ?? this.parentId,
      isParentEmployeeRequest:
          isParentEmployeeRequest ?? this.isParentEmployeeRequest,
      uid: uid ?? this.uid,
      userEmail: userEmail ?? this.userEmail,
      userName: userName ?? this.userName,
      replies: replies ?? this.replies,
      workTime: workTime ?? this.workTime,
      priority: priority ?? this.priority,
      isSuggestion: isSuggestion ?? this.isSuggestion,
      isApprovedSuggestion: isApprovedSuggestion ?? this.isApprovedSuggestion,
      isApproved: isApproved ?? this.isApproved,
      adminEmail: adminEmail ?? this.adminEmail,
      adminName: adminName ?? this.adminName,
    );
  }

  //? Empty EmployeeRequest
  factory EmployeeRequestModel.empty() {
    return const EmployeeRequestModel(id: null);
  }
}
