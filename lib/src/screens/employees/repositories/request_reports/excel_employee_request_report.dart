import 'dart:io';

import 'package:opti4t_tasks/src/screens/employees/models/employee_request_model.dart';
import 'package:path_provider/path_provider.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../auth/models/user_model.dart';

Future<String> generateEmployeeRequestsReportExcel({
  required UserModel? employee,
  required List<EmployeeRequestModel> employeeRequests,
  required DateTime fromDate,
  required DateTime toDate,
  required bool isEnglish,
}) async {
  final Workbook workbook = Workbook();
  final Worksheet sheet = workbook.worksheets[0];
  final Style headerStyle = workbook.styles.add('HeaderStyle');
  final Style dataStyle = workbook.styles.add('DataStyle');

  // Define styles
  headerStyle.hAlign = HAlignType.center;
  headerStyle.bold = true;
  dataStyle.hAlign = HAlignType.center;
  dataStyle.wrapText = true;

  // Define headers based on language
  List<String> headers = isEnglish
      ? ['Start Date', 'End Date', 'Work Days', 'Evaluation', 'Description']
      : ['الوصف', 'التقييم', 'أيام العمل', 'تاريخ الانتهاء', 'تاريخ البدء'];

  // Write headers
  for (int columnIndex = 1; columnIndex <= headers.length; columnIndex++) {
    final Range headerCell = sheet.getRangeByIndex(1, columnIndex);
    headerCell.setText(headers[columnIndex - 1]);
    headerCell.cellStyle = headerStyle;
  }

  int rowIndex = 2;
  for (var employeeRequest in employeeRequests) {
    final endDate = employeeRequest.endDate ?? DateTime.now();
    final isRequestClosedBeforeEndDate =
        employeeRequest.closedAt?.toDate().isBefore(endDate) ?? false;
    final isRequestClosedAtSameEndDay =
        employeeRequest.closedAt?.toDate().isSameDay(endDate) ?? false;
    final isRequestClosedAtAfterEndDayByDifference1Day =
        (employeeRequest.closedAt?.toDate().isAfter(endDate) ?? false) &&
            (employeeRequest.closedAt?.toDate().difference(endDate).inDays ==
                1);
    final evaluationText = isRequestClosedBeforeEndDate
        ? 'Excellent'
        : isRequestClosedAtSameEndDay
            ? 'Excellent'
            : isRequestClosedAtAfterEndDayByDifference1Day
                ? 'Good'
                : 'Bad';
    final workDays = (employeeRequest.date
                    ?.difference(
                        (employeeRequest.closedAt?.toDate()) ?? endDate)
                    .inDays ??
                0)
            .abs() +
        1;

    List<String> rowData = isEnglish
        ? [
            employeeRequest.date?.formatDateToStringWithTime ?? '-',
            employeeRequest.closedAt?.toDate().formatDateToStringWithTime ??
                '-',
            workDays.toString(),
            evaluationText,
            employeeRequest.description
          ]
        : [
            employeeRequest.description,
            evaluationText,
            workDays.toString(),
            employeeRequest.closedAt?.toDate().formatDateToStringWithTime ??
                '-',
            employeeRequest.date.formatDateToStringWithTime ?? '-'
          ];

    for (int columnIndex = 1; columnIndex <= rowData.length; columnIndex++) {
      final Range cell = sheet.getRangeByIndex(rowIndex, columnIndex);
      cell.setText(rowData[columnIndex - 1]);
      cell.cellStyle = dataStyle;
    }
    rowIndex++;
  }

  // Auto-fit columns for a better view
  for (int columnIndex = 1; columnIndex <= headers.length; columnIndex++) {
    sheet.autoFitColumn(columnIndex);
  }

  // Save the Excel file
  final Directory appDocDir = await getApplicationDocumentsDirectory();
  final String appDocPath = appDocDir.path;
  final String path =
      '$appDocPath/EmployeeRequestsReport_${DateTime.now().toIso8601String()}.xlsx';

  final List<int> bytes = workbook.saveAsStream();
  workbook.dispose();

  final File file = File(path);
  await file.writeAsBytes(bytes);

  Log.w('EmployeeRequests report Excel sheet saved at $path');

  return path;
}
