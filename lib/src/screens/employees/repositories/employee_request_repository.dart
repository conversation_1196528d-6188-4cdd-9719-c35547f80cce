import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti4t_tasks/src/core/consts/app_constants.dart';
import 'package:opti4t_tasks/src/core/consts/firestore_strings.dart';
import 'package:opti4t_tasks/src/core/services/firebase/firebase_storage_service.dart';
import 'package:opti4t_tasks/src/core/services/firebase/firestore_service.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/employees/models/employee_request_model.dart';
import 'package:xr_helper/xr_helper.dart';

final employeeRequestRepoProvider = Provider<EmployeeRequestRepository>((ref) {
  return EmployeeRequestRepository();
});

class EmployeeRequestRepository with BaseRepository {
  //? Add EmployeeRequest To Firestore
  Future<void> addEmployeeRequest({
    required EmployeeRequestModel employeeRequest,
    String? filePath,
    bool isParentEmployeeRequest = true,
  }) async {
    await baseFunction(
      () async {
        if (filePath != null && filePath.isNotEmpty) {
          final url = await uploadFile(
            employeeRequest: employeeRequest,
            filePath: filePath,
          );

          employeeRequest = employeeRequest.copyWith(
              attachmentUrl: url,
              isParentEmployeeRequest: isParentEmployeeRequest);
        }

        await FirestoreService.createData(
          collection: FirebaseStrings.employeeRequests,
          data: employeeRequest.toJson(),
        );
      },
    );
  }

  //? Update EmployeeRequest To Firestore
  Future<void> updateEmployeeRequest({
    required EmployeeRequestModel employeeRequest,
    String? filePath,
  }) async {
    await baseFunction(
      () async {
        if (filePath != null && filePath.isNotEmpty) {
          final url = await uploadFile(
            employeeRequest: employeeRequest,
            filePath: filePath,
          );

          employeeRequest = employeeRequest.copyWith(attachmentUrl: url);
        }

        await FirestoreService.updateData(
          collection: FirebaseStrings.employeeRequests,
          id: employeeRequest.id!,
          data: employeeRequest.toJson(),
        );
      },
    );
  }

  //? Reopen task
  Future<void> reOpenRequest({
    required EmployeeRequestModel request,
  }) async {
    await baseFunction(
      () async {
        await FirestoreService.updateData(
          collection: FirebaseStrings.employeeRequests,
          id: request.id!,
          data: request.toJsonReopen(),
        );
      },
    );
  }

  //? approveSuggestionRequest
  Future<void> approveSuggestionRequest({
    required EmployeeRequestModel request,
  }) async {
    await baseFunction(
      () async {
        await FirestoreService.updateData(
          collection: FirebaseStrings.employeeRequests,
          id: request.id!,
          data: request.toJsonApproveSuggestion(),
        );
      },
    );
  }

  //? upload file and get url
  Future<String> uploadFile({
    required EmployeeRequestModel employeeRequest,
    required String filePath,
  }) async {
    return await baseFunction(
      () async {
        final url = await FirebaseStorageService.uploadFile(
          fileName: employeeRequest.clientName,
          filePath: filePath,
        );
        return url;
      },
    );
  }

  //? Get EmployeeRequest Data From Same Login Id From Firestore
  Future<List<EmployeeRequestModel>> getEmployeeRequests(
      {required DateTime date, DateTime? toDate, String? userId}) async {
    try {
      List<EmployeeRequestModel> employeeRequests = [];

      if (UserModelHelper.isManager() && userId == null) return [];

      if (toDate == null) {
        employeeRequests = await FirestoreService.getDataBy2Fields(
            collection: FirebaseStrings.employeeRequests,
            field1: FirebaseStrings.uid,
            value1: userId ?? UserModelHelper.localUID(),
            field2: FirebaseStrings.date,
            value2: date.formatDateToString,
            builder: (id, data) {
              return EmployeeRequestModel.fromJson(id, data: data);
            });
      } else {
        employeeRequests = await FirestoreService.getDataBy3Fields(
            collection: FirebaseStrings.employeeRequests,
            field1: FirebaseStrings.uid,
            value1: userId ?? UserModelHelper.localUID(),
            field2: FirebaseStrings.date,
            value2: date.formatDateToString,
            field3: FirebaseStrings.date,
            value3: toDate.formatDateToString,
            builder: (id, data) {
              return EmployeeRequestModel.fromJson(id, data: data);
            });

        //? sort employeeRequests by date
        employeeRequests.sort((a, b) => a.date!.compareTo(b.date!));
      }
      return employeeRequests;
    } catch (e) {
      rethrow;
    }
  }

  //? Get EmployeeRequests Stream
  Stream<List<EmployeeRequestModel>> getEmployeeRequestsStream({
    required String? selectedEmployeeUid,
  }) {
    if (selectedEmployeeUid == null ||
        selectedEmployeeUid == AppConsts.allEmployees) {
      return FirestoreService.getStreamData(
        collection: FirebaseStrings.employeeRequests,
        withDateSort: true,
        builder: (id, data) {
          return EmployeeRequestModel.fromJson(
            id,
            data: data,
          );
        },
      );
    } else {
      return FirestoreService.getStreamDataByField(
        collection: FirebaseStrings.employeeRequests,
        field: FirebaseStrings.uid,
        value: selectedEmployeeUid,
        builder: (id, data) {
          return EmployeeRequestModel.fromJson(id, data: data);
        },
      );
    }
  }

  //getEmployeeRequestsFuture
  Future<List<EmployeeRequestModel>> getEmployeeRequestsFuture() async {
    try {
      List<EmployeeRequestModel> employeeRequests =
          await FirestoreService.getDataWithId(
        collection: FirebaseStrings.employeeRequests,
        builder: (id, data) {
          return EmployeeRequestModel.fromJson(id, data: data);
        },
      );
      return employeeRequests;
    } catch (e) {
      rethrow;
    }
  }

  //? Delete EmployeeRequest
  Future<void> deleteEmployeeRequest({
    required EmployeeRequestModel employeeRequest,
  }) async {
    await baseFunction(
      () async {
        await FirestoreService.deleteData(
          collection: FirebaseStrings.employeeRequests,
          id: employeeRequest.id!,
        );

        //? Delete file
        if (employeeRequest.attachmentUrl != null &&
            employeeRequest.attachmentUrl!.isNotEmpty) {
          await FirebaseStorageService.deleteFileByUrl(
            url: employeeRequest.attachmentUrl!,
          );
        }
      },
    );
  }

  // ? Approve Request ========================================
  Future<void> approveRequest({
    required EmployeeRequestModel request,
  }) async {
    await baseFunction(
      () async {
        await FirestoreService.updateData(
          collection: FirebaseStrings.employeeRequests,
          id: request.id!,
          data: request.toJsonApprove(),
        );
      },
    );
  }
}
