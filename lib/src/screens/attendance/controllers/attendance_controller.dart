import 'package:flutter/cupertino.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/attendance/models/attendance_model.dart';
import 'package:opti4t_tasks/src/screens/attendance/repositories/attendance_repository.dart';
import 'package:xr_helper/xr_helper.dart';

class AttendanceController extends BaseVM {
  final BuildContext context;
  final AttendanceRepository attendanceRepo;

  AttendanceController(
    this.context, {
    required this.attendanceRepo,
  });

  // * Get Attendances Stream ========================================
  Stream<List<AttendanceModel>> getAttendancesStream({
    required DateTime date,
    String? userId,
  }) {
    final attendances = attendanceRepo.getAttendancesStream(
      date: date,
      userId: userId,
    );

    return attendances;
  }

  // * Add Attendance ========================================
  Future<void> addAttendance({
    required AttendanceModel attendance,
  }) async {
    await baseFunction(
      context,
      () async {
        await attendanceRepo.addAttendance(attendance: attendance);
      },
      additionalFunction: (_) => afterAttendanceAction(
        _,
        title: context.tr.checkedInSuccessfully,
      ),
    );
  }

  // * Edit Attendance ========================================
  Future<void> editAttendance({
    required AttendanceModel attendance,
  }) async {
    await baseFunction(
      context,
      () async {
        await attendanceRepo.updateAttendance(attendance: attendance);
      },
      additionalFunction: (_) => afterAttendanceAction(
        _,
        title: attendance.isCheckedIn
            ? context.tr.checkedInSuccessfully
            : context.tr.checkedOutSuccessfully,
        color: attendance.isCheckedIn
            ? ColorManager.successColor
            : ColorManager.primaryColor,
      ),
    );
  }

  // * Get Attendances ========================================
  Future<List<AttendanceModel>> getAttendances({
    required DateTime date,
    DateTime? toDate,
    String? userId,
  }) async {
    return await attendanceRepo.getAttendances(
      date: date,
      toDate: toDate,
      userId: userId,
    );
  }

  // * Get Last Attendance Today ========================================
  Future<AttendanceModel?> getLastAttendanceToday() async {
    return await attendanceRepo.getLastAttendanceToday();
  }

  // * After Attendance Action ========================================
  void afterAttendanceAction(
    _, {
    required String title,
    Color color = AppColors.successColor,
  }) {
    Log.i('After Attendance Action: $title');
    context.back();

    context.showBarMessage(
      title,
      color: color,
    );
  }
}
