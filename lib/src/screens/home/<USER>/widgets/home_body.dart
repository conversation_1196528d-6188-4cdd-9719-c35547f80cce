import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/drop_downs/employees_drop_down.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/shared_widgets.dart';
import 'package:opti4t_tasks/src/screens/attendance/providers/attendance_providers.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/home/<USER>/widgets/attendance_status_widget.dart';
import 'package:opti4t_tasks/src/screens/home/<USER>/widgets/floating_buttons/add_task_floating_button.dart';
import 'package:opti4t_tasks/src/screens/home/<USER>/widgets/floating_buttons/check_in_out_floating_button.dart';
import 'package:opti4t_tasks/src/screens/home/<USER>/widgets/floating_buttons/close_all_tasks_floating_button.dart';
import 'package:opti4t_tasks/src/screens/tasks/view/widgets/task_list/task_list.dart';
import 'package:opti4t_tasks/src/screens/tasks/view/widgets/top_section_calendar.dart';
import 'package:xr_helper/xr_helper.dart';

import 'home_top_section.dart';

// final currentDate = DateTime.now();

class HomeBody extends HookConsumerWidget {
  final ValueNotifier<UserModel?>? selectedEmployee;
  final ValueNotifier<DateTime> selectedDayValue;

  const HomeBody(
      {super.key, this.selectedEmployee, required this.selectedDayValue});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    //? can add after today only
    final bool canAddTask = selectedDayValue.value.isToday() ||
        selectedDayValue.value.isAfter(DateTime.now());

    final params = (
      context,
      selectedDayValue.value,
      UserModelHelper.isAdmin()
          ? selectedEmployee?.value?.uid
          : UserModelHelper.localUID()
    );

    final getLastAttendanceFuture =
        ref.watch(getAttendancesStreamControllerProvider(params));

    final lastAttendance = getLastAttendanceFuture.when(
      data: (attendance) => attendance,
      loading: () => null,
      error: (error, _) => null,
    );

    Widget? floatingActionWidget() {
      if (canAddTask && !UserModelHelper.isManager()) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            if (!selectedDayValue.value.isToday())
              SizedBox(
                height: 100,
                width: 120,
              )
            else if (getLastAttendanceFuture.isLoading)
              SizedBox(
                height: 100,
                width: 120,
                child: Padding(
                  padding: const EdgeInsets.only(top: AppSpaces.largePadding),
                  child: LoadingWidget(),
                ),
              ).paddingSymmetric(horizontal: AppSpaces.xLargePadding)
            else
              CheckInOutFloatingButton(
                      lastAttendance: lastAttendance?.lastOrNull)
                  .paddingSymmetric(horizontal: AppSpaces.xLargePadding),
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CloseAllTasksFloatingButton(
                  selectedDayValue: selectedDayValue,
                  selectedEmployee: selectedEmployee,
                ),
                context.mediumGap,
                AddTaskFloatingButton(
                  selectedDate: selectedDayValue,
                  selectedEmployee: selectedEmployee,
                ),
              ],
            ),
          ],
        );
      }

      return null;
    }

    return Scaffold(
      floatingActionButton: floatingActionWidget(),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const HomeTopSection(),

              context.mediumGap,

              //! Top Section Calender
              CalendarWidget(
                selectedDayValue: selectedDayValue,
                selectedEmployee: selectedEmployee,
              ),

              if (UserModelHelper.isManager() ||
                  UserModelHelper.canViewAllTasks()) ...[
                Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: AppSpaces.mediumPadding),
                  margin: const EdgeInsets.only(
                      bottom: AppSpaces.smallPadding,
                      top: AppSpaces.largePadding,
                      left: AppSpaces.largePadding,
                      right: AppSpaces.largePadding),
                  child: EmployeesDropDown(
                    selectedEmployee: selectedEmployee,
                  ),
                ),
              ],

              // attendance status
              if (lastAttendance != null &&
                  lastAttendance.lastOrNull != null) ...[
                context.mediumGap,
                AttendanceStatusWidget(
                  lastAttendance: lastAttendance.lastOrNull!,
                ).paddingSymmetric(
                  horizontal: AppSpaces.mediumPadding,
                  vertical: AppSpaces.smallPadding,
                ),
              ],

              //! Tasks
              TasksList(
                selectedDayValue: selectedDayValue,
                selectedEmployee: selectedEmployee,
              ),
              context.xLargeGap,
            ],
          ),
        ),
      ),
    );
  }
}
