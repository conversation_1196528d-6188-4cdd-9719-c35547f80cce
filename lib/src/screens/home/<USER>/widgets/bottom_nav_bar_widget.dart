import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:xr_helper/xr_helper.dart';

class BottomNavBarWidget extends HookConsumerWidget {
  final ValueNotifier<int> currentIndex;

  const BottomNavBarWidget({super.key, required this.currentIndex});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final index0 = currentIndex.value == 0;
    final index1 = currentIndex.value == 1;
    final index2 = currentIndex.value == 2;
    final index3 = currentIndex.value == 3;
    final index4 = currentIndex.value == 4;
    final index5 = currentIndex.value == 5;

    return Container(
      padding: EdgeInsets.only(
        right: context.isEng ? AppSpaces.xSmallPadding : 0,
        left: context.isEng ? 0 : AppSpaces.xSmallPadding,
      ),
      decoration: BoxDecoration(
        color: ColorManager.secondaryColor.withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          topRight: Radius.circular(AppRadius.mediumRadius),
          topLeft: Radius.circular(AppRadius.mediumRadius),
        ),
        // boxShadow: bottomNavBoxShadow
      ),
      child: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        unselectedItemColor: ColorManager.bottomNavIconColor,
        selectedItemColor: ColorManager.primaryColor,
        selectedLabelStyle: const TextStyle(
          color: ColorManager.primaryColor,
          fontWeight: FontWeight.bold,
        ),
        unselectedLabelStyle: const TextStyle(
          color: ColorManager.primaryColor,
        ),
        selectedFontSize: 14,
        unselectedFontSize: 14,
        iconSize: 22,
        elevation: 0,
        backgroundColor: Colors.transparent,
        currentIndex: currentIndex.value,
        onTap: (index) {
          currentIndex.value = index;
        },
        items: [
          BottomNavigationBarItem(
            icon: Icon(
              Icons.home,
              color: index0
                  ? ColorManager.primaryColor
                  : ColorManager.bottomNavIconColor,
            ),
            label: context.tr.home,
          ),
          BottomNavigationBarItem(
            icon: Icon(
              CupertinoIcons.calendar,
              color: index1
                  ? ColorManager.primaryColor
                  : ColorManager.bottomNavIconColor,
            ),
            label: context.tr.vacations,
          ),
          BottomNavigationBarItem(
            icon: Icon(
              CupertinoIcons.person_2,
              color: index2
                  ? ColorManager.primaryColor
                  : ColorManager.bottomNavIconColor,
            ),
            label: UserModelHelper.isAdmin()
                ? context.tr.employees
                : context.tr.requests,
          ),
          BottomNavigationBarItem(
            icon: Icon(
              UserModelHelper.isAdmin() ? Icons.people : Icons.work_outline,
              color: index3
                  ? ColorManager.primaryColor
                  : ColorManager.bottomNavIconColor,
            ),
            label: UserModelHelper.isAdmin()
                ? context.tr.clients
                : context.tr.projects,
          ),
          if (UserModelHelper.isAdmin()) ...[
            BottomNavigationBarItem(
              icon: Icon(
                Icons.dashboard,
                color: index4
                    ? ColorManager.primaryColor
                    : ColorManager.bottomNavIconColor,
              ),
              label: context.tr.reports,
            ),
            BottomNavigationBarItem(
              icon: Icon(
                Icons.meeting_room,
                color: index5
                    ? ColorManager.primaryColor
                    : ColorManager.bottomNavIconColor,
              ),
              label: context.tr.meetings,
            ),
          ],
        ],
      ),
    );
  }
}
