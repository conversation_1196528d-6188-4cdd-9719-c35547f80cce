import 'package:flutter/material.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/attendance/models/attendance_model.dart';
import 'package:opti4t_tasks/src/screens/reports/repositories/pdf_reports/attendance/attendance_report_helper.dart';
import 'package:xr_helper/xr_helper.dart';

class AttendanceStatusWidget extends StatelessWidget {
  final AttendanceModel lastAttendance;

  const AttendanceStatusWidget({super.key, required this.lastAttendance});

  @override
  Widget build(BuildContext context) {
    final checkedInAt = lastAttendance.checkedInAt;
    final checkedOutAt = lastAttendance.checkedOutAt;
    final partCheckoutReason = lastAttendance.partCheckoutReason;

    final latestStatus = lastAttendance.lastAction == LastActionEnum.checkedIn
        ? context.tr.checkedIn
        : lastAttendance.lastAction == LastActionEnum.checkedOut
            ? context.tr.checkedOut
            : context.tr.partCheckOut;

    final latestTime = lastAttendance.lastAction == LastActionEnum.checkedIn
        ? checkedInAt.last
        : lastAttendance.lastAction == LastActionEnum.checkedOut
            ? checkedOutAt ?? checkedInAt.last
            : partCheckoutReason.keys.last;
    // checkedOutAt ?? checkedInAt.last ?? partCheckoutReason.keys.last;

    final latestColor = lastAttendance.lastAction == LastActionEnum.checkedIn
        ? ColorManager.successColor
        : lastAttendance.lastAction == LastActionEnum.checkedOut
            ? ColorManager.grey
            : ColorManager.primaryColor;

    final containerColor = Colors.blueGrey.shade900;

    // Combine all times into a single list and sort them
    final allTimes = [
      ...checkedInAt.map((time) => {'time': time, 'type': 'checkedIn'}),
      if (checkedOutAt != null) {'time': checkedOutAt, 'type': 'checkedOut'},
      ...partCheckoutReason.keys
          .map((time) => {'time': time, 'type': 'partCheckOut'}),
    ];

    allTimes.sort((a, b) {
      final timeComparison = createDateTimeFromTime(a['time']!)
          .compareTo(createDateTimeFromTime(b['time']!));
      if (timeComparison != 0) {
        return timeComparison;
      }
      // Ensure the order: check-in, part check-out, check-out
      if (a['type'] == 'checkedIn') return -1;
      if (b['type'] == 'checkedIn') return 1;
      if (a['type'] == 'partCheckOut') return -1;
      if (b['type'] == 'partCheckOut') return 1;
      return 0;
    });

    return Container(
      decoration: BoxDecoration(
        color: containerColor,
        borderRadius: BorderRadius.circular(AppRadius.mediumRadius),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppRadius.mediumRadius),
        child: ExpansionTile(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppRadius.mediumRadius),
          ),
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    color: latestColor,
                  ),
                  context.mediumGap,
                  Text(
                    latestStatus,
                    style: context.whiteLabelLarge.copyWith(color: latestColor),
                  ),
                ],
              ),
              Text(
                latestTime ?? '-',
                style: context.whiteLabelMedium.copyWith(color: latestColor),
              ).paddingOnly(top: 1),
            ],
          ),
          childrenPadding: const EdgeInsets.all(AppSpaces.mediumPadding),
          children: [
            SingleChildScrollView(
              child: ListView(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                children: allTimes.map((entry) {
                  final time = entry['time']!;
                  final type = entry['type']!;
                  final color = type == 'checkedIn'
                      ? ColorManager.successColor
                      : type == 'checkedOut'
                          ? ColorManager.grey
                          : ColorManager.primaryColor;
                  final icon = type == 'checkedIn'
                      ? Icons.fingerprint
                      : type == 'checkedOut'
                          ? Icons.check_circle_outline
                          : Icons.error_outline;
                  final reason =
                      type == 'partCheckOut' ? partCheckoutReason[time] : null;
                  final title = type == 'checkedIn'
                      ? context.tr.checkedIn
                      : type == 'checkedOut'
                          ? context.tr.checkedOut
                          : context.tr.partCheckOut;

                  return _buildAttendanceContainer(
                    context,
                    title: title,
                    time: time,
                    color: color,
                    icon: icon,
                    reason: reason,
                    isLastItem: allTimes.last == entry,
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttendanceContainer(
    BuildContext context, {
    required String title,
    required String time,
    required Color color,
    required IconData icon,
    String? reason,
    bool isLastItem = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              padding: const EdgeInsets.all(2.0),
              width: 20.0,
              height: 20.0,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: ColorManager.white,
                border: Border.all(
                  color: color,
                  width: 1,
                ),
              ),
              child: CircleAvatar(
                backgroundColor: color,
                radius: 1,
              ),
            ),
            CustomPaint(
              size: Size(0, isLastItem ? 70 : 100),
              painter: LinePainter(color: color),
            ),
          ],
        ),
        context.smallGap,
        Expanded(
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppSpaces.mediumPadding),
            margin: const EdgeInsets.only(bottom: AppSpaces.smallPadding),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      title,
                      style: context.whiteLabelLarge
                          .copyWith(color: color, fontWeight: FontWeight.bold),
                    ),
                    Icon(
                      icon,
                      color: color,
                    ),
                  ],
                ),
                context.mediumGap,
                Padding(
                  padding: const EdgeInsets.only(top: AppSpaces.smallPadding),
                  child: Row(
                    children: [
                      Icon(
                        Icons.access_time,
                        color: color,
                        size: 19,
                      ).paddingOnly(bottom: 2),
                      context.xSmallGap,
                      Expanded(
                        child: Text(
                          reason != null
                              ? '$time - ${context.tr.reason}: $reason'
                              : time,
                          style:
                              context.whiteLabelMedium.copyWith(color: color),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class LinePainter extends CustomPainter {
  final Color color;

  LinePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2;

    canvas.drawLine(
        Offset(size.width / 2, 0), Offset(size.width / 2, size.height), paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
