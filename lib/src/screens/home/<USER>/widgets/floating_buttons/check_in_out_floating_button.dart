import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/services/local_auth/local_auth_service.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/shared_widgets.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/attendance/models/attendance_model.dart';
import 'package:opti4t_tasks/src/screens/attendance/providers/attendance_providers.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:xr_helper/xr_helper.dart';

class CheckInOutFloatingButton extends HookConsumerWidget {
  final AttendanceModel? lastAttendance;

  const CheckInOutFloatingButton({
    super.key,
    required this.lastAttendance,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isInWorkHours = DateTime.now().hour >= 8 && DateTime.now().hour <= 23;
    final isCheckedOutForToday = lastAttendance?.isCheckedOut ?? false;

    if (!isInWorkHours) {
      return const SizedBox();
    }

    return FloatingActionButton.extended(
      backgroundColor: lastAttendance?.isCheckedIn == true
          ? ColorManager.errorColor
          : ColorManager.successColor,
      onPressed: () {
        if (isCheckedOutForToday) {
          context.showBarMessage(
            context.tr.youHaveAlreadyCheckedOutForToday,
            isError: true,
          );
          return;
        }
        showDialog(
            context: context,
            builder: (_) => CheckInOutDialog(
                  lastAttendance: lastAttendance,
                ));
      },
      icon: const Icon(Icons.fingerprint),
      label: Text(
        lastAttendance?.isCheckedIn == true
            ? context.tr.checkOut
            : context.tr.checkIn,
        style: context.whiteSubTitle,
      ),
    ).paddingOnly(bottom: AppSpaces.xSmallPadding);
  }
}

class CheckInOutDialog extends HookConsumerWidget {
  final AttendanceModel? lastAttendance;

  const CheckInOutDialog({
    super.key,
    required this.lastAttendance,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final attendanceController =
        ref.watch(attendanceControllerNotifierProvider(context));

    final formKey = useMemoized(() => GlobalKey<FormState>());

    final isPartCheckout = useState(false);
    final reasonController = useTextEditingController();

    final partCheckoutReasons =
        useState<Map<String, String>>(lastAttendance?.partCheckoutReason ?? {});

    final shouldCheckOut = lastAttendance?.isCheckedIn == true;

    void closeAttendance() async {
      if (!formKey.currentState!.validate()) return;

      if (!await LocalAuthService.authenticate(context)) return;

      if (shouldCheckOut && isPartCheckout.value) {
        partCheckoutReasons.value[DateTime.now().formatTimeToString] =
            reasonController.text;
      }

      final attendance = AttendanceModel(
        id: lastAttendance?.id,
        uid: UserModelHelper.localUID(),
        date: lastAttendance?.date ?? DateTime.now(),
        checkedInAt: shouldCheckOut
            ? lastAttendance?.checkedInAt ?? []
            : [
                ...lastAttendance?.checkedInAt ?? [],
                DateTime.now().formatTimeToString
              ],
        checkedOutAt: shouldCheckOut && !isPartCheckout.value
            ? DateTime.now().formatTimeToString
            : lastAttendance?.checkedOutAt,
        userEmail: UserModelHelper.signedUser().email,
        userName: UserModelHelper.signedUser().name,
        createdAt: lastAttendance?.createdAt ?? Timestamp.now(),
        partCheckoutReason: partCheckoutReasons.value,
        lastAction: shouldCheckOut
            ? isPartCheckout.value
                ? LastActionEnum.partCheckedOut
                : LastActionEnum.checkedOut
            : LastActionEnum.checkedIn,
      );

      if (lastAttendance != null) {
        await attendanceController.editAttendance(attendance: attendance);
      } else {
        await attendanceController.addAttendance(attendance: attendance);
      }
    }

    if (attendanceController.isLoading) {
      return const Center(
        child: LoadingWidget(),
      );
    }

    final title = shouldCheckOut ? context.tr.checkOut : context.tr.checkIn;

    final description = shouldCheckOut
        ? context.tr.areYouSureYouWantToCheckOut
        : context.tr.areYouSureYouWantToCheckIn;

    final buttonLabel = context.tr.submit;

    return Form(
      key: formKey,
      child: Dialog(
          child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const CircleAvatar(
                backgroundColor: ColorManager.primaryColor,
                child: Icon(
                  Icons.playlist_add_check_outlined,
                  color: Colors.white,
                ),
              ),
              context.mediumGap,
              Text(
                title,
                style: context.whiteTitle,
              ),
            ],
          ),

          context.largeGap,

          //? Are you sure you want to close this attendance?
          Text(
            description,
            style: context.whiteSubTitle,
          ),

          if (shouldCheckOut) ...[
            context.mediumGap,
            SwitchListTile(
              title:
                  Text(context.tr.partCheckOut, style: context.whiteSubTitle),
              value: isPartCheckout.value,
              onChanged: (value) {
                isPartCheckout.value = value;
              },
            ),
            if (isPartCheckout.value) ...[
              context.mediumGap,
              BaseTextField(
                controller: reasonController,
                isWhiteText: true,
                isRequired: isPartCheckout.value,
                hint: context.tr.enterReason,
                label: context.tr.reason,
                maxLines: 3,
              ),
            ],
          ] else
            context.mediumGap,

          context.largeGap,

          Row(
            children: [
              //? Cancel Button
              Expanded(
                child: TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(context.tr.cancel, style: context.whiteSubTitle),
                ),
              ),

              context.mediumGap,

              Expanded(
                  flex: 2,
                  child: Button(
                    label: buttonLabel,
                    onPressed: closeAttendance,
                    textColor: Colors.white,
                  )),
            ],
          ).sized(
            height: 45,
          )
        ],
      ).paddingAll(AppSpaces.largePadding - 4)),
    );
  }
}
