import 'package:flutter/material.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/clients/view/projects/projects_list/projects_list.dart';
import 'package:opti4t_tasks/src/screens/employees/views/employee_requests_screen/widgets/employee_requests_list/employee_requests_list.dart';
import 'package:opti4t_tasks/src/screens/employees/views/employee_requests_screen/widgets/floating_buttons/add_employee_request_floating_button.dart';
import 'package:opti4t_tasks/src/screens/employees/views/employees_page/employees_screen.dart';
import 'package:opti4t_tasks/src/screens/home/<USER>/widgets/home_body.dart';
import 'package:opti4t_tasks/src/screens/home/<USER>/widgets/home_top_section.dart';
import 'package:opti4t_tasks/src/screens/reports/view/reports_screen.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../clients/view/clients/clients_screen.dart';
import '../../../meetings/view/meetings_screen.dart';
import '../../../vacations/view/vacations_screen.dart';

class SelectedWidget extends StatelessWidget {
  final ValueNotifier<int> currentIndex;
  final ValueNotifier<UserModel?> selectedEmployee;
  final ValueNotifier<DateTime> selectedDayValue;

  const SelectedWidget({
    super.key,
    required this.currentIndex,
    required this.selectedEmployee,
    required this.selectedDayValue,
  });

  @override
  Widget build(BuildContext context) {
    return UserModelHelper.isAdmin()
        ? _buildAdminView(context)
        : _buildUserView(context);
  }

  Widget _buildAdminView(BuildContext context) {
    switch (currentIndex.value) {
      case 0:
        return HomeBody(
          selectedEmployee: selectedEmployee,
          selectedDayValue: selectedDayValue,
        );
      case 1:
        return VacationScreen(selectedEmployee: selectedEmployee);
      case 2:
        return const EmployeesScreen();
      case 3:
        return const ClientsScreen();
      case 4:
        return const ReportsScreen();
      default:
        return const MeetingsScreen();
    }
  }

  Widget _buildUserView(BuildContext context) {
    if (currentIndex.value == 0) {
      return HomeBody(
        selectedEmployee: selectedEmployee,
        selectedDayValue: selectedDayValue,
      );
    } else if (currentIndex.value == 1) {
      return VacationScreen(selectedEmployee: selectedEmployee);
    } else if (currentIndex.value == 2) {
      return Scaffold(
        floatingActionButton: AddEmployeeRequestFloatingButton(),
        body: Column(
          children: [
            context.largeGap,
            HomeTopSection(
              title: context.tr.requests,
            ),
            Expanded(child: const EmployeeRequestsList().scroll()),
          ],
        ),
      );
    } else {
      return Scaffold(
        body: Column(
          children: [
            context.largeGap,
            HomeTopSection(
              title: context.tr.projects,
            ),
            Expanded(
              child: ProjectsList().scroll(),
            ),
          ],
        ),
      );
    }
  }
}
