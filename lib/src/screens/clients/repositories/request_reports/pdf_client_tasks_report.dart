import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/screens/clients/models/client_model.dart';
import 'package:opti4t_tasks/src/screens/reports/repositories/report_repository_helper.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/src/widgets/font.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:xr_helper/xr_helper.dart';

import '../../../auth/models/user_model.dart';

Future<pw.Document> generateClientsReportPDF(BuildContext appContext,
    {required UserModel? employee,
    required ClientRequestModel? parentClient,
    required List<ClientRequestModel> projects,
    required DateTime fromDate,
    required DateTime toDate}) async {
  final arabicFont = Font.ttf(
    await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
  );

  final pdf = pw.Document();

  // final isEnglish = appContext.isAppEnglish;

  pw.Widget buildTable(ClientRequestModel parentProject,
      List<ClientRequestModel> childProjects) {
    return pw.Table(
      columnWidths: {
        0: const pw.FlexColumnWidth(1),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1),
      },
      border: pw.TableBorder.all(),
      children: [
        pw.TableRow(
          children: [
            baseText(
              appContext.tr.startDate,
              arabicFont: arabicFont,
              isBold: true,
            ),
            baseText(
              appContext.tr.endDate,
              arabicFont: arabicFont,
              isBold: true,
            ),
            baseText(
              appContext.tr.description,
              arabicFont: arabicFont,
              isBold: true,
            ),
          ],
        ),
        ...childProjects.map(
          (childProject) => pw.TableRow(
            children: [
              baseText(
                childProject.createdAt?.toDate().formatDateToStringWithTime ??
                    '-',
                arabicFont: arabicFont,
              ),
              baseText(
                childProject.closedAt?.toDate().formatDateToStringWithTime ??
                    '-',
                arabicFont: arabicFont,
              ),
              baseText(
                childProject.description,
                arabicFont: arabicFont,
              ),
            ],
          ),
        ),
      ],
    );
  }

  pdf.addPage(
    pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      build: (pw.Context context) {
        return <pw.Widget>[
          pw.Header(
            level: 0,
            child: pw.Text(
              parentClient?.name ?? '',
              textScaleFactor: 2,
              textAlign: pw.TextAlign.center,
              textDirection: pw.TextDirection.rtl,
              style: pw.TextStyle(
                font: arabicFont,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
          ),
          pw.Header(
            level: 0,
            child: pw.Text(
              '${employee?.name ?? appContext.tr.allEmployees} (${fromDate.formatDateToString} - ${toDate.formatDateToString})',
              textScaleFactor: 2,
              textAlign: pw.TextAlign.center,
              textDirection: pw.TextDirection.rtl,
              style: pw.TextStyle(
                font: arabicFont,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
          ),
          ...projects
              .where((project) =>
                  project.isParentClient == true &&
                  project.clientId == parentClient?.id)
              .map(
            (parentProject) {
              final childProjects = projects
                  .where((project) => project.parentId == parentProject.id)
                  .toList();
              return pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    parentProject.name,
                    textDirection: pw.TextDirection.rtl,
                    textAlign: pw.TextAlign.center,
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontWeight: pw.FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                  buildTable(parentProject, childProjects),
                  pw.SizedBox(height: 20),
                ],
              );
            },
          ),
        ];
      },
    ),
  );

  return pdf;
}
