import 'package:flutter/cupertino.dart';
import 'package:opti4t_tasks/src/core/consts/app_constants.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/services/send_email/send_email_service.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/clients/models/client_model.dart';
import 'package:opti4t_tasks/src/screens/clients/repositories/clients_repository.dart';
import 'package:xr_helper/xr_helper.dart';

class ClientRequestController extends BaseVM {
  final BuildContext context;
  final ClientRepository clientRepo;

  ClientRequestController(
    this.context, {
    required this.clientRepo,
  });

  // * Get Clients Stream ========================================
  Stream<List<ClientRequestModel>> getClientsStream(
      {String? selectedEmployeeUid}) {
    final clients =
        clientRepo.getClientsStream(selectedEmployeeUid: selectedEmployeeUid);

    return clients;
  }

  // * Get Projects Stream ========================================
  Stream<List<ClientRequestModel>> getProjectsStream(
      {String? selectedEmployeeUid}) {
    final clients =
        clientRepo.getProjectsStream(selectedEmployeeUid: selectedEmployeeUid);

    return clients;
  }

  //getClientsFuture
  Future<List<ClientRequestModel>> getClientsFuture({
    bool isProject = false,
  }) {
    final clients = clientRepo.getClientsFuture(isProject: isProject);

    return clients;
  }

  // * Add Client ========================================
  Future<void> addClient({
    required String? filePath,
    required ClientRequestModel client,
    bool isProject = false,
  }) async {
    await baseFunction(
      context,
      () async {
        await clientRepo.addClient(
            client: client,
            filePath: filePath,
            isProject: isProject,
            isParentClient: client.isParentClient == true);
      },
      additionalFunction: (_) => afterClientAction(
        context,
        title: context.tr.addedSuccessfully,
      ),
    );
  }

  // * Edit Client ========================================
  // Future<void> editClient({
  //   required ClientModel client,
  //   String? filePath,
  // }) async {
  //   await baseFunction(
  //     context,
  //     () async {
  //       await clientRepo.updateClient(client: client, filePath: filePath);
  //     },
  //     additionalFunction: (_) => afterClientAction(_,
  //         title: context.tr.editedSuccessfully,
  //         color: ColorManager.successColor),
  //   );
  // }
  Future<void> editClient({
    required ClientRequestModel client,
    bool isClosed = false,
    bool isReply = false,
    String? filePath,
    bool isProject = false,
  }) async {
    await baseFunction(
      context,
      () async {
        await clientRepo.updateClient(
          client: client,
          filePath: filePath,
          isProject: isProject,
        );

        if (isClosed) {
          final closedDurationInDays = client.createdAt
                      ?.toDate()
                      .difference(client.closedAt!.toDate())
                      .inDays ==
                  0
              ? 1
              : client.createdAt
                  ?.toDate()
                  .difference(client.closedAt!.toDate())
                  .inDays;

          await SendEmailService.sendEmail(
            subject: "تم إغلاق الطلب بنجاح في $closedDurationInDays يوم",
            body: client.description,
            recipient: [client.userEmail, AppConsts.managerEmail],
          );
        }
      },
      additionalFunction: (_) => afterClientAction(_,
          title: isClosed
              ? context.tr.closedSuccessfully
              : isReply
                  ? context.tr.addedSuccessfully
                  : context.tr.editedSuccessfully,
          color:
              isClosed ? ColorManager.primaryColor : ColorManager.successColor),
    );
  }

  // * Reopen Request  ========================================
  Future<void> reOpenRequest({
    required ClientRequestModel request,
    bool isProject = false,
  }) async {
    await baseFunction(
      context,
      () async {
        await clientRepo.reOpenRequest(
          request: request,
          isProject: isProject,
        );
      },
      additionalFunction: (_) => afterClientAction(_,
          title: context.tr.reopenedSuccessfully,
          color: ColorManager.primaryColor),
    );
  }

  // closeAllRequests
  Future<void> closeAllRequests({
    required String clientId,
    bool isProject = false,
  }) async {
    await baseFunction(
      context,
      () async {
        await clientRepo.closeAllRequests(
          clientId: clientId,
          isProject: isProject,
        );
      },
      additionalFunction: (_) => afterClientAction(_,
          title: context.tr.closedSuccessfully,
          color: ColorManager.primaryColor),
    );
  }

  // * Delete Client ========================================
  Future<void> deleteClient({
    required ClientRequestModel client,
    bool isProject = false,
  }) async {
    await baseFunction(
      context,
      () async {
        await clientRepo.deleteClient(
          client: client,
          isProject: isProject,
        );
      },
      additionalFunction: (_) => afterClientAction(_,
          title: context.tr.deletedSuccessfully,
          color: ColorManager.errorColor),
    );
  }

  // * Approve Request ========================================
  Future<void> approveRequest({
    required ClientRequestModel request,
  }) async {
    await baseFunction(
      context,
      () async {
        await clientRepo.approveRequest(
          request: request,
        );
      },
      additionalFunction: (_) => afterClientAction(_,
          title: context.tr.approvedSuccessfully,
          color: ColorManager.successColor),
    );
  }

  // * After Client Action ========================================
  void afterClientAction(
    _, {
    required String title,
    Color color = AppColors.successColor,
  }) {
    context.back();

    context.showBarMessage(
      title,
      color: color,
    );
  }
}
