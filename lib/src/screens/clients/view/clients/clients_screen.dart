import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/tab_bar/base_tab_bar.widget.dart';
import 'package:opti4t_tasks/src/screens/clients/view/clients/widgets/client_list/client_list.dart';
import 'package:opti4t_tasks/src/screens/clients/view/clients/widgets/filtered_clients/filtered_clients_list.dart';
import 'package:opti4t_tasks/src/screens/clients/view/clients/widgets/floating_buttons/add_project_floating_button.dart';
import 'package:opti4t_tasks/src/screens/home/<USER>/widgets/home_top_section.dart';
import 'package:xr_helper/xr_helper.dart';

import '../projects/projects_list/projects_list.dart';
import 'widgets/floating_buttons/add_client_floating_button.dart';

class ClientsScreen extends HookConsumerWidget {
  const ClientsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabController = useTabController(initialLength: 2);
    final selectedTab = useState<int>(0);

    Widget floatingActionWidget() {
      if (selectedTab.value == 0) {
        return const AddClientFloatingButton();
      }

      return const AddProjectFloatingButton();
    }

    return Scaffold(
      floatingActionButton: floatingActionWidget(),
      body: SafeArea(
        child: Column(
          children: [
            const HomeTopSection(),
            Expanded(
              child: Column(
                children: [
                  TabBar(
                    controller: tabController,
                    tabs: [
                      Tab(text: context.tr.allClients),
                      Tab(text: context.tr.filteredClients),
                    ],
                  ),
                  context.mediumGap,
                  Expanded(
                    child: TabBarView(
                      controller: tabController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        Column(
                          children: [
                            //! Clients
                            BaseTabBarWidget(
                              selectedTab: selectedTab,
                              tabs: [
                                context.tr.clients,
                                context.tr.projects,
                              ],
                            ),

                            Expanded(
                              child: IndexedStack(
                                index: selectedTab.value,
                                children: [
                                  ClientsList().scroll(),
                                  ProjectsList().scroll(),
                                ],
                              ),
                            ),
                          ],
                        ),

                        //! Filtered Clients
                        const FilteredClientsList()
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
