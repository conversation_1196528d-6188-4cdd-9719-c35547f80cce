import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/clients/models/client_model.dart';
import 'package:opti4t_tasks/src/screens/clients/view/clients/widgets/client_card/client_card.dart';
import 'package:xr_helper/xr_helper.dart';

class ClientsListWidget extends HookConsumerWidget {
  final List<ClientRequestModel> clients;
  final ValueNotifier<UserModel?>? selectedEmployee;
  final bool fromHome;

  const ClientsListWidget({
    super.key,
    required this.clients,
    required this.selectedEmployee,
    this.fromHome = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final parentClients = clients
        .where((client) =>
            client.isParentClient == true || client.isParentClient == null)
        .toList();

    // Check if all parent clients have no requests
    bool allClientsHaveNoRequests = parentClients.every((client) {
      final requests = clients
          .where((childClient) =>
              childClient.parentId == client.id &&
              (UserModelHelper.isAdmin() && selectedEmployee?.value == null
                  ? true
                  : childClient.uid == selectedEmployee?.value?.uid))
          .toList();
      return requests.isEmpty;
    });

    // If all parent clients have no requests and fromHome is true, return "No Requests" message
    if (allClientsHaveNoRequests && fromHome) {
      return Center(
        child: Text(
          context.tr.noRequests,
          style: context.whiteLabelLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListView.builder(
          shrinkWrap: true,
          itemCount: parentClients.length,
          physics: const NeverScrollableScrollPhysics(),
          padding: EdgeInsets.only(
            bottom: UserModelHelper.isManager()
                ? AppSpaces.xLargePadding + 15
                : AppSpaces.largePadding,
            right: AppSpaces.mediumPadding,
            left: AppSpaces.mediumPadding,
          ),
          itemBuilder: (context, index) {
            final client = parentClients[index];

            final requests = clients
                .where((childClient) =>
                    childClient.parentId == client.id &&
                    (UserModelHelper.isAdmin() &&
                            selectedEmployee?.value == null
                        ? true
                        : childClient.uid == selectedEmployee?.value?.uid))
                .toList();

            final nonApprovedRequests = requests
                .where((element) => element.isApproved == false)
                .toList();

            if (nonApprovedRequests.isEmpty && fromHome) {
              return const SizedBox.shrink();
            }

            return ClientCard(client: client, requests: requests)
                .paddingSymmetric(
              vertical: AppSpaces.smallPadding,
            );
          },
        ),
      ],
    );
  }
}
