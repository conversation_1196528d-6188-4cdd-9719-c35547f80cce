import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/extensions/riverpod_extensions.dart';
import 'package:opti4t_tasks/src/core/services/notifications/local_notifications_service.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/clients/models/client_model.dart';
import 'package:opti4t_tasks/src/screens/clients/providers/clients_providers.dart';
import 'package:opti4t_tasks/src/screens/clients/view/clients/widgets/client_list/widgets/main_client_list_widget.dart';
import 'package:xr_helper/xr_helper.dart';

class ClientsList extends ConsumerWidget {
  final ValueNotifier<UserModel?>? selectedEmployee;
  final bool fromHome;

  const ClientsList({
    super.key,
    this.selectedEmployee,
    this.fromHome = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final params = (context, selectedEmployee?.value?.uid);

    final clients = ref.watch(getClientsStreamControllerProvider(params));

    return clients.get(data: (clients) {
      List<ClientRequestModel> filteredClients = clients;

      if (!UserModelHelper.isManager() && fromHome) {
        filteredClients =
            clients.where((client) => !client.isCompleted).toList();
      }

      // Check if there are any unclosed requests and trigger a local notification
      if (filteredClients.any((client) => !client.isCompleted) &&
          !UserModelHelper.isAdmin()) {
        LocalNotificationService.subscribeToUnClosedRequests(context);
      }

      if (clients.isEmpty) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircleAvatar(
              backgroundColor: ColorManager.primaryColor,
              maxRadius: 60,
              child: Icon(
                Icons.people,
                color: Colors.white,
                size: 40,
              ),
            ),
            context.largeGap,
            Text(
              context.tr.noClients,
              style: context.whiteHeadLine,
            ),
          ],
        );
      }

      return ClientsListWidget(
        clients: filteredClients,
        fromHome: fromHome,
        selectedEmployee: selectedEmployee,
      );
    });
  }
}
