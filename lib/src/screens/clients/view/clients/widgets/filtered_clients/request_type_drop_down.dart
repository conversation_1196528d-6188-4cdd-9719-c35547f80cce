import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:xr_helper/xr_helper.dart';

class RequestTypeDropDown extends HookWidget {
  final ValueNotifier<String> selectedRequestType;

  const RequestTypeDropDown({super.key, required this.selectedRequestType});

  @override
  Widget build(BuildContext context) {
    final requestTypes = [
      context.tr.allRequests,
      context.tr.closedRequests,
      context.tr.openedRequests,
    ];

    return BaseDropDown(
      selectedValue: selectedRequestType.value,
      onChanged: (newValue) {
        selectedRequestType.value = newValue!;
      },
      data: requestTypes,
      label: context.tr.requestsType,
      isWhiteText: true,
    );
  }
}
