import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/shared_widgets.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/clients/controllers/filtered_client_controller.dart';
import 'package:opti4t_tasks/src/screens/clients/models/client_model.dart';
import 'package:xr_helper/xr_helper.dart';

import 'send_filtered_clients_dialog.dart';

class FilterClientsExportButtons extends HookConsumerWidget {
  final ValueNotifier<UserModel?> selectedEmployee;
  final ValueNotifier<ClientRequestModel?> selectedClient;
  final (
    ValueNotifier<DateTime> from,
    ValueNotifier<DateTime> to
  ) selectedDates;
  final ValueNotifier<String> selectedType;
  final ValueNotifier<List<ClientRequestModel>> requestsByClient;

  const FilterClientsExportButtons({
    super.key,
    required this.selectedEmployee,
    required this.selectedClient,
    required this.selectedDates,
    required this.selectedType,
    required this.requestsByClient,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final reportsController =
        ref.watch(filteredClientControllerProvider(context));

    final generatedReport = useState<String>('');

    void export({
      bool isExcel = false,
    }) async {
      if (selectedEmployee.value == null &&
          selectedType.value == context.tr.projects) {
        context.showBarMessage(context.tr.pleaseSelectEmployee, isError: true);
        return;
      }

      if (requestsByClient.value.isNotEmpty) {
        generatedReport.value = await reportsController.generateClientReport(
            context,
            employee: selectedEmployee.value,
            selectedDates: (selectedDates.$1.value, selectedDates.$2.value),
            recipientEmail: selectedEmployee.value?.email ?? '',
            requestsByClient: requestsByClient.value,
            parentClient: selectedClient.value ?? ClientRequestModel(),
            selectedType: selectedType.value,
            isExcel: isExcel);

        context.showBarMessage(context.tr.reportGeneratedSuccessfully,
            color: ColorManager.primaryColor);
      }
    }

    return Column(
      children: [
        if (selectedClient.value != null ||
            selectedType.value != context.tr.projects) ...[
          if (generatedReport.value.isNotEmpty) ...[
            context.largeGap,

            //? Open report
            _OpenReportAndSendEmailButtons(
              generatedReport: generatedReport,
              selectedEmployee: selectedEmployee,
              selectedFromDate: selectedDates.$1,
              selectedToDate: selectedDates.$2,
            ),
          ],

          context.largeGap,

          //! Button
          Button(
            isLoading: reportsController.isLoading,
            loadingWidget: const LoadingWidget(),
            label: context.tr.exportPdf,
            onPressed: () => export(),
            textColor: Colors.white,
          ).sized(height: 50),

          context.largeGap,

          //! Button
          Button(
            isLoading: reportsController.isLoading,
            loadingWidget: const LoadingWidget(),
            color: Colors.green.shade500,
            label: context.tr.exportExcel,
            onPressed: () => export(isExcel: true),
            textColor: Colors.white,
          ).sized(height: 50),
        ],
      ],
    ).paddingSymmetric(
      horizontal: AppSpaces.largePadding,
    );
  }
}

class _OpenReportAndSendEmailButtons extends ConsumerWidget {
  final ValueNotifier<UserModel?> selectedEmployee;
  final ValueNotifier<DateTime> selectedFromDate;
  final ValueNotifier<DateTime> selectedToDate;
  final ValueNotifier<String> generatedReport;

  const _OpenReportAndSendEmailButtons({
    required this.selectedEmployee,
    required this.selectedFromDate,
    required this.selectedToDate,
    required this.generatedReport,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final reportsController =
        ref.watch(filteredClientControllerProvider(context));

    return Row(
      children: [
        Expanded(
          child: Button(
            label: context.tr.openReport,
            color: ColorManager.secondaryColor,
            onPressed: () {
              reportsController.openClientPDF(generatedReport.value);
            },
            textColor: Colors.white,
          ),
        ),

        context.mediumGap,

        //? Send Email
        GestureDetector(
          onTap: () {
            showDialog(
              context: context,
              builder: (context) => SendFilteredClientsDialog(
                employee: selectedEmployee.value,
                fromDate: selectedFromDate.value,
                toDate: selectedToDate.value,
                recipientEmail: selectedEmployee.value?.email ?? '',
                generatedReport: generatedReport,
              ),
            );
          },
          child: const CircleAvatar(
            backgroundColor: Colors.white,
            child: Icon(
              Icons.send,
              color: Colors.black,
            ),
          ),
        ),
      ],
    ).sized(
      height: 50,
    );
  }
}
