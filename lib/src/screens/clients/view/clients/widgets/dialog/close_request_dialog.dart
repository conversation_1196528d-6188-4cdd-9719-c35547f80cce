import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/shared_widgets.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/clients/models/client_model.dart';
import 'package:opti4t_tasks/src/screens/clients/providers/clients_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class CloseRequestDialog extends HookConsumerWidget {
  final ClientRequestModel? request;
  final bool closeAllRequests;
  final bool isProject;

  const CloseRequestDialog({
    super.key,
    this.request,
    this.closeAllRequests = false,
    this.isProject = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final clientController =
        ref.watch(clientControllerNotifierProvider(context));
    final completedRequest = (request?.isCompleted ?? false);

    void closeRequest() async {
      if (closeAllRequests) {
        await clientController.closeAllRequests(
          isProject: isProject,
          clientId: request!.id ?? '',
        );
      } else {
        if (completedRequest) {
          await clientController.reOpenRequest(
            request: request!,
            isProject: isProject,
          );
        } else {
          final copiedTask = request!.copyWith(closedAt: Timestamp.now());

          await clientController.editClient(
            client: copiedTask,
            isClosed: completedRequest ? false : true,
            isProject: isProject,
          );
        }
      }
    }

    if (clientController.isLoading) {
      return const Center(
        child: LoadingWidget(),
      );
    }

    final title = completedRequest
        ? context.tr.reopenTask
        : closeAllRequests
            ? context.tr.closeAllRequests
            : context.tr.closeRequest;

    final description = completedRequest
        ? context.tr.areYouSureYouWantToReopenThisRequest
        : closeAllRequests
            ? context.tr.areYouSureYouWantToCloseAllRequests
            : context.tr.areYouSureYouWantToCloseThisRequest;

    final buttonLabel = completedRequest
        ? context.tr.reopenRequest
        : closeAllRequests
            ? context.tr.closeAllRequests
            : context.tr.closeRequest;

    return Dialog(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const CircleAvatar(
              backgroundColor: ColorManager.primaryColor,
              child: Icon(
                Icons.playlist_add_check_outlined,
                color: Colors.white,
              ),
            ),
            context.mediumGap,
            Text(
              title,
              style: context.whiteTitle,
            ),
          ],
        ),

        context.largeGap,

        //? Are you sure you want to close this task?
        Text(
          description,
          style: context.whiteSubTitle,
        ),

        context.xLargeGap,

        Row(
          children: [
            //? Cancel Button
            Expanded(
              child: TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(context.tr.cancel, style: context.whiteSubTitle),
              ),
            ),

            context.mediumGap,

            Expanded(
                flex: 2,
                child: Button(
                  label: buttonLabel,
                  onPressed: closeRequest,
                  textColor: Colors.white,
                )),
          ],
        ).sized(
          height: 45,
        )
      ],
    ).paddingAll(AppSpaces.largePadding - 4));
  }
}
