import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/shared_widgets.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/clients/models/client_model.dart';
import 'package:opti4t_tasks/src/screens/clients/providers/clients_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class DeleteRequestDialog extends HookConsumerWidget {
  final ClientRequestModel request;
  final bool isClient;
  final bool isProject;

  const DeleteRequestDialog({
    super.key,
    required this.request,
    this.isClient = false,
    this.isProject = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final clientController =
        ref.watch(clientControllerNotifierProvider(context));

    void deleteClient() async {
      await clientController.deleteClient(
        client: request,
        isProject: isProject,
      );
    }

    if (clientController.isLoading) {
      return const Center(
        child: LoadingWidget(),
      );
    }

    final title = isProject
        ? context.tr.deleteProject
        : isClient
            ? context.tr.deleteClient
            : context.tr.deleteRequest;

    final description = isProject
        ? context.tr.areYouSureYouWantToDeleteThisProject
        : isClient
            ? context.tr.areYouSureYouWantToDeleteThisClient
            : context.tr.areYouSureYouWantToDeleteThisRq;

    final buttonLabel = context.tr.delete;

    return Dialog(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const CircleAvatar(
              backgroundColor: ColorManager.primaryColor,
              child: Icon(
                Icons.playlist_add_check_outlined,
                color: Colors.white,
              ),
            ),
            context.mediumGap,
            Text(
              title,
              style: context.whiteTitle,
            ),
          ],
        ),

        context.largeGap,

        //? Are you sure you want to close this client?
        Text(
          description,
          style: context.whiteSubTitle,
        ),

        context.xLargeGap,

        Row(
          children: [
            //? Cancel Button
            Expanded(
              child: TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text(context.tr.cancel, style: context.whiteSubTitle),
              ),
            ),

            context.mediumGap,

            Expanded(
                flex: 2,
                child: Button(
                  label: buttonLabel,
                  onPressed: deleteClient,
                  textColor: Colors.white,
                )),
          ],
        ).sized(
          height: 45,
        )
      ],
    ).paddingAll(AppSpaces.largePadding - 4));
  }
}
