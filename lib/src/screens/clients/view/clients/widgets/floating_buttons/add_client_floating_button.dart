import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/services/media/controller/media_controller.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/drop_downs/employees_drop_down.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/shared_widgets.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/clients/models/client_model.dart';
import 'package:opti4t_tasks/src/screens/clients/providers/clients_providers.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../core/shared_widgets/drop_downs/multi_employees_drop_down.dart';
import '../../../../../auth/models/user_model_helper.dart';

class AddClientFloatingButton extends ConsumerWidget {
  final String? clientId;

  const AddClientFloatingButton({
    super.key,
    this.clientId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaController = ref.watch(mediaPickerControllerProvider);

    final isParentClient = clientId == null;

    final canAddClients =
        UserModelHelper.canViewAllTasks() || UserModelHelper.isManager();

    if (!isParentClient) {
      return const CircleAvatar(
        backgroundColor: ColorManager.primaryColor,
        child: Icon(
          Icons.add,
          color: Colors.white,
        ),
      ).onTap(() => showDialog(
          context: context,
          builder: (_) => AddClientDialog(
                clientId: clientId,
              )).then((value) => mediaController.clearFiles()));
    }

    if (!canAddClients) {
      return const SizedBox();
    }

    return FloatingActionButton(
      onPressed: () => showDialog(
          context: context,
          builder: (_) => AddClientDialog(
                clientId: clientId,
              )).then((value) => mediaController.clearFiles()),
      child: const Icon(Icons.add),
    );
  }
}

class AddClientDialog extends HookConsumerWidget {
  final ClientRequestModel? client; //? For Edit
  final String? clientId; //?  To Check If Client Or Request

  const AddClientDialog({super.key, this.client, this.clientId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final clientController =
        ref.watch(clientControllerNotifierProvider(context));
    final mediaController = ref.watch(mediaPickerControllerProvider);

    final nameController = useTextEditingController(text: client?.name);

    final descriptionController =
        useTextEditingController(text: client?.description);
    final clientProgramsController =
        useTextEditingController(text: client?.clientPrograms);

    final testModulesController =
        useTextEditingController(text: client?.testModules);
    final testModulesEndDate = useState(client?.testModulesEndDate);
    final nameFocusNode = useFocusNode();
    final descriptionFocusNode = useFocusNode();

    final formKey = useState(GlobalKey<FormState>());

    final isEdit = client != null;

    final isAddingClient = clientId == null;

    final title = isEdit
        ? context.tr.editClient
        : isAddingClient
            ? context.tr.addNewClient
            : context.tr.addNewRequest;

    useEffect(() {
      if (isAddingClient) {
        nameFocusNode.requestFocus();
      } else {
        descriptionFocusNode.requestFocus();
      }

      return () {};
    }, []);

    final selectedEmployee = useState<UserModel?>(null);
    final selectedEmployees = useState<List<UserModel?>?>(null);
    final haveMaintenanceContract =
        useState(client?.haveMaintenanceContract ?? false);

    final haveSoldContract = useState(client?.haveSoldContract ?? false);
    final isActive = useState(client?.isActive ?? false);

    void validateAndAddOrEditClient() async {
      if (formKey.value.currentState!.validate()) {
        if (isEdit) {
          final copiedClient = client!.copyWith(
            name: nameController.text,
            selectedEmployees: selectedEmployees.value
                    ?.map(
                      (e) => e?.uid ?? '',
                    )
                    .toList() ??
                [],
            description: descriptionController.text,
            uid: selectedEmployee.value?.uid ?? '',
            userEmail: selectedEmployee.value?.email ?? '',
            userName: selectedEmployee.value?.name ?? '',
            clientPrograms: clientProgramsController.text,
            haveMaintenanceContract: haveMaintenanceContract.value,
            haveSoldContract: haveSoldContract.value,
            isActive: isActive.value,
            testModules: testModulesController.text,
            testModulesEndDate: testModulesController.text.isEmpty
                ? null
                : testModulesEndDate.value,
          );

          await clientController.editClient(
            client: copiedClient,
            filePath: mediaController.filePath,
          );
        } else {
          final client = ClientRequestModel(
            parentId: clientId,
            name: nameController.text,
            description: descriptionController.text,
            clientPrograms: clientProgramsController.text,
            haveMaintenanceContract: haveMaintenanceContract.value,
            isActive: isActive.value,
            haveSoldContract: haveSoldContract.value,
            testModules: testModulesController.text,
            testModulesEndDate: testModulesController.text.isEmpty
                ? null
                : testModulesEndDate.value,
            selectedEmployees: selectedEmployees.value
                    ?.map(
                      (e) => e?.uid ?? '',
                    )
                    .toList() ??
                [],
            uid: selectedEmployee.value?.uid ?? '',
            userEmail: selectedEmployee.value?.email ?? '',
            userName: selectedEmployee.value?.name ?? '',
            createdAt: Timestamp.fromDate(DateTime.now()),
            date: DateTime.now(),
            isParentClient: isAddingClient,
          );

          await clientController.addClient(
            client: client,
            filePath: mediaController.filePath,
          );
        }
      }
    }

    if (clientController.isLoading) {
      return const Center(
        child: LoadingWidget(),
      );
    }

    return Dialog(
        child: Form(
      key: formKey.value,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpaces.largePadding - 4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const CircleAvatar(
                  backgroundColor: ColorManager.primaryColor,
                  child: Icon(
                    Icons.notes,
                    color: Colors.white,
                  ),
                ),
                context.mediumGap,
                Text(
                  title,
                  style: context.whiteTitle,
                ),
              ],
            ),
            if (isAddingClient) ...[
              context.largeGap,

              //! Name
              BaseTextField(
                label: context.tr.name,
                focusNode: nameFocusNode,
                onChanged: (_) {},
                isWhiteText: true,
                controller: nameController,
                validator: (value) {
                  return Validations.mustBeNotEmpty(value,
                      emptyMessage: context.tr.fieldCannotBeEmpty);
                },
              ),
            ],
            if (!isAddingClient) ...[
              _AddRequestFields(
                client: client,
                descriptionController: descriptionController,
                descriptionFocusNode: descriptionFocusNode,
                selectedEmployee: selectedEmployee,
                oldEmployeeUid: client?.uid,
              ),
            ] else ...[
              context.largeGap,
              BaseTextField(
                controller: clientProgramsController,
                label: context.tr.clientPrograms,
                isWhiteText: true,
                minLines: 1,
                maxLines: 3,
                textInputType: TextInputType.multiline,
                validator: (value) {
                  return Validations.mustBeNotEmpty(value,
                      emptyMessage: context.tr.fieldCannotBeEmpty);
                },
              ),
              context.largeGap,
              BaseTextField(
                isRequired: false,
                controller: testModulesController,
                label: context.tr.testModules,
                hint: context.isEng
                    ? 'e.g: Module 1, Module 2'
                    : 'مثال: حسابات, نقل',
                isWhiteText: true,
                minLines: 1,
                maxLines: 3,
                textInputType: TextInputType.multiline,
                validator: (value) {
                  return Validations.mustBeNotEmpty(value,
                      emptyMessage: context.tr.fieldCannotBeEmpty);
                },
              ),
              context.largeGap,
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(context.tr.testModulesEndDate,
                      style: context.whiteSubTitle),
                  context.smallGap,
                  //! From Date
                  BaseDatePicker(
                    selectedDateNotifier: testModulesEndDate,
                    label: context.tr.date,
                    isWhite: true,
                    align: context.isAppEnglish
                        ? Alignment.centerLeft
                        : Alignment.centerRight,
                  ),
                ],
              ),
              context.mediumGap,
              SwitchListTile(
                title: Text(context.tr.isActive),
                value: isActive.value,
                onChanged: (value) {
                  isActive.value = value;
                },
              ),
              context.mediumGap,
              SwitchListTile(
                title: Text(context.tr.haveMaintenanceContract),
                value: haveMaintenanceContract.value,
                onChanged: (value) {
                  haveMaintenanceContract.value = value;
                },
              ),
              context.mediumGap,
              SwitchListTile(
                title: Text(context.tr.haveSoldContract),
                value: haveSoldContract.value,
                onChanged: (value) {
                  haveSoldContract.value = value;
                },
              ),
              context.largeGap,
              MultiEmployeesDropDown(
                selectedEmployees: selectedEmployees,
                isRequired: false,
                label: context.tr.chooseEmployees,
                employeeIds: client?.selectedEmployees,
              ),
            ],

            context.largeGap,

            //! Submit Button
            Button(
              label: isEdit ? context.tr.edit : context.tr.submit,
              onPressed: validateAndAddOrEditClient,
              textColor: Colors.white,
            )
          ],
        ),
      ),
    ));
  }
}

class _AddRequestFields extends StatelessWidget {
  final TextEditingController descriptionController;
  final FocusNode descriptionFocusNode;
  final ClientRequestModel? client;
  final ValueNotifier<UserModel?> selectedEmployee;
  final String? oldEmployeeUid;

  const _AddRequestFields({
    required this.descriptionController,
    required this.descriptionFocusNode,
    required this.client,
    required this.selectedEmployee,
    required this.oldEmployeeUid,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        context.largeGap,

        //! Description
        BaseTextField(
          label: context.tr.description,
          isWhiteText: true,
          controller: descriptionController,
          focusNode: descriptionFocusNode,
          minLines: 1,
          maxLines: 5,
          textInputType: TextInputType.multiline,
          validator: (value) {
            return Validations.mustBeNotEmpty(value,
                emptyMessage: context.tr.fieldCannotBeEmpty);
          },
        ),

        context.largeGap,

        EmployeesDropDown(
          selectedEmployee: selectedEmployee,
          isRequired: false,
          label: context.tr.assignToEmployee,
          employeeId: oldEmployeeUid,
        ),

        context.largeGap,

        SinglePickImageWidget(
          networkImage: client?.attachmentUrl,
        ),
      ],
    );
  }
}
