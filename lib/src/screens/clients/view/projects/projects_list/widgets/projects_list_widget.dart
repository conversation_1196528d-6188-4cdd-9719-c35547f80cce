import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/clients/models/client_model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../projects_card/project_card.dart';

class ProjectsListWidget extends HookConsumerWidget {
  final List<ClientRequestModel> clients;
  final ValueNotifier<ClientRequestModel?>? selectedClient;
  final bool fromFilter;

  const ProjectsListWidget({
    super.key,
    required this.clients,
    this.selectedClient,
    this.fromFilter = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final parentClients = clients
        .where((client) =>
            client.isParentClient == true || client.isParentClient == null)
        .where((element) =>
            selectedClient?.value == null ||
            element.clientId == selectedClient?.value?.id)
        .toList();

    return ListView.builder(
      shrinkWrap: true,
      itemCount: parentClients.length,
      physics: fromFilter
          ? const BouncingScrollPhysics()
          : const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.only(
        bottom: UserModelHelper.isManager()
            ? AppSpaces.xLargePadding + 15
            : AppSpaces.largePadding,
        right: AppSpaces.mediumPadding,
        left: AppSpaces.mediumPadding,
      ),
      itemBuilder: (context, index) {
        final client = parentClients[index];

        final requests = clients
            .where((childClient) => childClient.parentId == (client.id))
            .toList();

        return ProjectCard(client: client, requests: requests).paddingSymmetric(
          vertical: AppSpaces.smallPadding,
        );
      },
    );
  }
}
