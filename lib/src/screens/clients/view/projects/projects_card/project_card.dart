import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/animated/lottie_icon.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/clients/models/client_model.dart';
import 'package:opti4t_tasks/src/screens/clients/view/projects/projects_card/widgets/request_card/project_request_card.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../clients/widgets/dialog/close_request_dialog.dart';
import '../../clients/widgets/floating_buttons/add_project_floating_button.dart';

class ProjectCard extends ConsumerWidget {
  final ClientRequestModel client;
  final List<ClientRequestModel> requests;

  const ProjectCard({super.key, required this.client, required this.requests});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAllCompleted = requests.every((element) => element.isCompleted);

    //     key: ValueKey(client.id),
    //       confirmDismiss: (direction) async {
    //         if (UserModelHelper.isManager()) {
    //           return await showDialog(
    //               context: context,
    //               builder: (_) => DeleteRequestDialog(
    //                     request: client,
    //                     isClient: true,
    //                     isProject: true,
    //                   ));
    //         } else {
    //           return false;
    //         }
    //       },
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpaces.smallPadding),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppRadius.smallRadius),
        color: ColorManager.secondaryColor.withOpacity(.1),
      ),
      child: ExpansionTile(
        initiallyExpanded: true,
        childrenPadding: const EdgeInsets.only(bottom: AppSpaces.smallPadding),
        tilePadding: const EdgeInsets.symmetric(
            horizontal: AppSpaces.mediumPadding,
            vertical: AppSpaces.xSmallPadding),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.smallRadius),
        ),
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //? Date
                Text(
                  client.createdAt?.toDate().formatDateToStringWithTime ?? '',
                  style: context.whiteHint.copyWith(
                    fontSize: 13,
                  ),
                ),

                context.smallGap,

                SizedBox(
                  width: context.width * 0.5,
                  child: Text(
                    client.name,
                    style: context.whiteTitle.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),

                context.smallGap,

                SizedBox(
                  width: context.width * 0.5,
                  child: Text(
                    '${context.tr.client}: ${client.clientName}',
                    style: context.whiteTitle.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                context.smallGap,

                SizedBox(
                  width: context.width * 0.5,
                  child: Text(
                    context.isEng
                        ? client.typeTranslateEn
                        : client.typeTranslateAr,
                    style: context.whiteTitle.copyWith(
                        fontWeight: FontWeight.bold,
                        color: client.type == "completed" ||
                                client.type == "ready_for_marketing"
                            ? ColorManager.successColor
                            : client.type == "in_progress"
                                ? ColorManager.primaryColor
                                : client.type == "under_review"
                                    ? ColorManager.errorColor
                                    : ColorManager.primaryColor),
                  ),
                ),
              ],
            ),
            Column(
              children: [
                if (client.haveMaintenanceContract == true) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: AppSpaces.smallPadding,
                        vertical: AppSpaces.xSmallPadding),
                    decoration: BoxDecoration(
                      color: ColorManager.primaryColor,
                      borderRadius:
                          BorderRadius.circular(AppRadius.smallRadius),
                    ),
                    child: Text(
                      context.tr.maintenance,
                      style: context.whiteLabelMedium.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  context.mediumGap,
                ],
                Column(
                  children: [
                    context.largeGap,
                    //! Add
                    if (!UserModelHelper.isManager()) ...[
                      Row(
                        children: [
                          if (requests.isNotEmpty)
                            CircleAvatar(
                                    backgroundColor: isAllCompleted
                                        ? ColorManager.primaryColor
                                        : ColorManager.secondaryColor,
                                    child: Icon(
                                      isAllCompleted
                                          ? Icons.check
                                          : CupertinoIcons.doc_checkmark_fill,
                                      color: Colors.white,
                                    ))
                                .onTapWithRipple(() => isAllCompleted
                                    ? context.showBarMessage(
                                        context.tr.allTasksCompleted)
                                    : showDialog(
                                        context: context,
                                        builder: (_) => CloseRequestDialog(
                                              request: client,
                                              isProject: true,
                                              closeAllRequests: true,
                                            )))
                                .paddingSymmetric(
                                    horizontal: AppSpaces.smallPadding),
                          AddProjectFloatingButton(
                            projectId: client.id,
                          ),
                        ],
                      ),
                    ],
                    if (UserModelHelper.isManager() ||
                        UserModelHelper.canViewAllTasks()) ...[
                      if (UserModelHelper.canViewAllTasks()) context.smallGap,

                      //! Edit
                      const CircleAvatar(
                              backgroundColor: ColorManager.successColor,
                              child: BaseLottieIcon('assets/animated/edit.json',
                                  width: 20, height: 20))
                          .onTapWithRipple(
                              () => showDialog(
                                    context: context,
                                    builder: (_) => AddProjectDialog(
                                      client: client,
                                    ),
                                  ),
                              radius: 5),

                      context.smallGap,
                    ],
                  ],
                )
              ],
            ),
          ],
        ),
        children: requests.isEmpty
            ? [
                Center(
                  child: Text(context.tr.noTasks,
                      style: context.whiteTitle.copyWith(
                        fontWeight: FontWeight.bold,
                      )),
                ).paddingAll(AppSpaces.mediumPadding)
              ]
            : requests
                .map((request) => ProjectRequestsCardWidget(
                      request: request,
                      clientRequestIndex: requests.indexOf(request) + 1,
                    ))
                .toList(),
      ),
    );
  }
}
