// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'eg_firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class EGFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDRhD7lKQAM_X7UdASM6DHYS3nP-wCyD70',
    appId: '1:777375926305:web:2983dfc326c7b5f2617b60',
    messagingSenderId: '777375926305',
    projectId: 'opti4it-tasks',
    authDomain: 'opti4it-tasks.firebaseapp.com',
    databaseURL: 'https://opti4it-tasks-default-rtdb.firebaseio.com',
    storageBucket: 'opti4it-tasks.appspot.com',
    measurementId: 'G-9P4HYCQDHW',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyASu_kewDUCx8rfWJLA2-xsCatN0ybwAgE',
    appId: '1:777375926305:android:2ba2ba63bd6b2f92617b60',
    messagingSenderId: '777375926305',
    projectId: 'opti4it-tasks',
    databaseURL: 'https://opti4it-tasks-default-rtdb.firebaseio.com',
    storageBucket: 'opti4it-tasks.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAOS2sVO3kCeRDDjDNu7I9ui4xM-gS_6Kg',
    appId: '1:777375926305:ios:13657f30896d3687617b60',
    messagingSenderId: '777375926305',
    projectId: 'opti4it-tasks',
    databaseURL: 'https://opti4it-tasks-default-rtdb.firebaseio.com',
    storageBucket: 'opti4it-tasks.appspot.com',
    iosBundleId: 'com.opti4it.opti4tTasks',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAOS2sVO3kCeRDDjDNu7I9ui4xM-gS_6Kg',
    appId: '1:777375926305:ios:564f842aa410cf0a617b60',
    messagingSenderId: '777375926305',
    projectId: 'opti4it-tasks',
    databaseURL: 'https://opti4it-tasks-default-rtdb.firebaseio.com',
    storageBucket: 'opti4it-tasks.appspot.com',
    iosBundleId: 'com.opti4it.opti4tTasks.RunnerTests',
  );
}
