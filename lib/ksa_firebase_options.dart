// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class KSAFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBKnvKSh0Mh_gOhcT6zXNs7C8FJrRVzQOM',
    appId: '1:656588044028:web:09e88a2d181f0cf5244979',
    messagingSenderId: '656588044028',
    projectId: 'opti4it-tasks-uae',
    authDomain: 'opti4it-tasks-uae.firebaseapp.com',
    storageBucket: 'opti4it-tasks-uae.appspot.com',
    measurementId: 'G-9GPM0WCBRM',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBck7rg-v5RZ78Cg4i6NLjMIbWbs_PDlS8',
    appId: '1:656588044028:android:ba6b00d59fa869f6244979',
    messagingSenderId: '656588044028',
    projectId: 'opti4it-tasks-uae',
    storageBucket: 'opti4it-tasks-uae.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyD4u_OFledVQYqvRTkJp9Zs-AxGvmo-yZI',
    appId: '1:656588044028:ios:05f6ce5ea80cc234244979',
    messagingSenderId: '656588044028',
    projectId: 'opti4it-tasks-uae',
    storageBucket: 'opti4it-tasks-uae.appspot.com',
    iosBundleId: 'com.opti4it.opti4tTasksUAEApp',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyD4u_OFledVQYqvRTkJp9Zs-AxGvmo-yZI',
    appId: '1:656588044028:ios:9cedddddc6561900244979',
    messagingSenderId: '656588044028',
    projectId: 'opti4it-tasks-uae',
    storageBucket: 'opti4it-tasks-uae.appspot.com',
    iosBundleId: 'com.opti4it.opti4tTasks',
  );
}
