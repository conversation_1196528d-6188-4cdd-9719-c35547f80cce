part of xr_helper;

class BaseVM extends ChangeNotifier {
  //! Loading ====================================
  bool _isLoading = false;

  bool get isLoading => _isLoading;

  set setIsLoading(bool isLoading) {
    _isLoading = isLoading;
    notifyListeners();
  }

  //! Handle Success ====================================
  void handleSuccess(BuildContext context,
      [FlushBarType type = FlushBarType.add, bool isBack = true]) {
    if (type != FlushBarType.delete && isBack) {
      context.back();
    }

    // context.showFlushBar(type: type);

    setIsLoading = false;
  }

  //! Base Function With Exception Handling ====================================
  Future<dynamic> baseFunction(BuildContext context,
      Function() mainFunction, {
        FlushBarType? type,
        bool isBack = true,
        bool isLoading = true,
        Function(BuildContext context)? additionalFunction,
        Function(dynamic error)? onError,
      }) async {
    try {
      final isDelete = type == FlushBarType.delete;

      if (!isDelete) {
        setIsLoading = isLoading;
      }

      final function = await mainFunction();

      if (context.mounted) {
        await _handleSuccess(
          context,
          type: type,
          isBack: isBack,
          additionalFunction: additionalFunction,
        );
      }

      return function;
    } on FetchDataException catch (error) {
      Log.e(error.toString());

      setIsLoading = false;

       if(onError != null) {
         onError(error);
       }

    } on SocketException {
      setIsLoading = false;

      // if (context.mounted) {
      //   context.showFlushBar(type: FlushBarType.noInternet);
      // }
    } on TimeoutException {
      setIsLoading = false;

      // if (context.mounted) {
      //   context.showFlushBar(type: FlushBarType.timeOut);
      // }
    } catch (error, s) {
      Log.e(error.toString() + s.toString());
      setIsLoading = false;
    }
  }

  Future<void> _handleSuccess(BuildContext context,
      {FlushBarType? type,
        bool isBack = true,
        Function(BuildContext context)? additionalFunction}) async {
    if (additionalFunction != null) {
      await additionalFunction(context);
    }

    if (type != null) {
      if (!context.mounted) return;
      handleSuccess(context, type, isBack);
    } else {
      setIsLoading = false;
    }
  }
}
