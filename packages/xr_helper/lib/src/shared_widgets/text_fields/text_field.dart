part of xr_helper;

class BaseTextField extends StatelessWidget {
  final FocusNode? focusNode;
  final TextEditingController? controller;
  final TextInputType textInputType;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final TextAlign textAlign;
  final Function()? onTap;
  final EdgeInsetsGeometry? contentPadding;
  final Widget? icon;
  final Widget? suffixIcon;
  final String? label;
  final String? hint;
  final int? minLines;
  final int maxLines;
  final bool isWhiteText;
  final String? ignoringMessage;
  final String? Function(String?)? validator;
  final bool isObscure;
  final bool isRequired;
  final String? initialValue;
  final bool enabled;
  final bool readOnly;
  final bool unFocus;
  final String? title;
  final TextInputAction? textInputAction;

  const BaseTextField(
      {super.key,
      this.ignoringMessage,
      this.focusNode,
      this.controller,
      this.isObscure = false,
      this.unFocus = true,
      this.onTap,
      this.hint,
      this.icon,
      this.suffixIcon,
      this.label,
      this.onChanged,
      this.onSubmitted,
      this.initialValue,
      this.textAlign = TextAlign.start,
      this.contentPadding = const EdgeInsets.all(AppSpaces.mediumPadding),
      this.textInputType = TextInputType.text,
      this.minLines,
      this.maxLines = 1,
      this.isWhiteText = false,
      this.isRequired = true,
      this.enabled = true,
      this.readOnly = false,
      this.validator,
      this.textInputAction,
      this.title});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Text(
            title!,
            style: context.subTitle,
          ),
          context.smallGap,
        ],

        //! Text Field
        _textField(context),
      ],
    );
  }

  Widget _textField(BuildContext context) {
    validations(value) {
      if (textInputType == TextInputType.number) {
        return Validations.numbersOnly(value);
      } else if (textInputType == TextInputType.emailAddress) {
        return Validations.email(value);
      } else if (textInputType == TextInputType.phone) {
        return Validations.phoneNumber(value);
      }
      return Validations.mustBeNotEmpty(value);
    }

    return HookBuilder(builder: (context) {
      final direction = useState(TextDirection.ltr);

      return TextFormField(
        textDirection: direction.value,
        scrollPadding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom +
                AppSpaces.mediumPadding),
        style: isWhiteText ? context.whiteLabelLarge : null,
        onTapOutside: (e) => unFocus ? FocusScope.of(context).unfocus() : null,
        focusNode: focusNode,
        obscureText: isObscure,
        textInputAction: textInputAction,
        enabled: enabled,
        readOnly: readOnly,
        minLines: minLines,
        controller: controller,
        keyboardType: textInputType,
        inputFormatters: [
          if (textInputType == TextInputType.number)
            FilteringTextInputFormatter.allow(RegExp(r'[0-9.-]'))
        ],
        textAlign: direction.value == TextDirection.rtl
            ? TextAlign.right
            : TextAlign.left,
        onTap: onTap,
        onChanged: (value) {
          direction.value =
              value.isEnglishText ? TextDirection.ltr : TextDirection.rtl;

          if (onChanged != null) onChanged!(value);
        },
        onFieldSubmitted: onSubmitted,
        initialValue: initialValue,
        maxLines: maxLines,
        validator: isRequired ? (validator ?? validations) : null,
        decoration: InputDecoration(
          hintText: hint,
          hintStyle: context.greyLabelMedium,
          labelStyle:
              isWhiteText ? context.whiteLabelLarge : context.labelLarge,
          contentPadding: contentPadding,
          labelText: label,
          suffixIcon: suffixIcon,
          prefixIcon: icon != null
              ? Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: AppSpaces.mediumPadding,
                      vertical: AppSpaces.smallPadding),
                  child: icon,
                )
              : null,
        ),
      );
    });
  }
}
