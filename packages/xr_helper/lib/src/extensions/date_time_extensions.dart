part of xr_helper;

extension DateTimeExtentions on DateTime? {
  String get formatDateToString {
    if (this == null) return '';
    return DateFormat('yyyy-MM-dd', 'en').format(this!);
  }

  String get formatDateToStringWithTime {
    if (this == null) return '';
    return DateFormat('yyyy-MM-dd, hh:mm a', 'en').format(this!);
  }

  String get formatTimeToString {
    if (this == null) return '';
    return DateFormat('hh:mm a', 'en').format(this!);
  }

  //dayName
  String get dayNameAr {
    if (this == null) return '';
    return DateFormat('EEEE', 'ar').format(this!);
  }

  String get dayNameEn {
    if (this == null) return '';
    return DateFormat('EEEE', 'en').format(this!);
  }

  bool isToday() {
    if (this == null) return false;
    final now = DateTime.now();
    return now.year == this!.year &&
        now.month == this!.month &&
        now.day == this!.day;
  }

  //isSameDay
  bool isSameDay(DateTime other) {
    if (this == null) return false;
    return this!.year == other.year &&
        this!.month == other.month &&
        this!.day == other.day;
  }
}
