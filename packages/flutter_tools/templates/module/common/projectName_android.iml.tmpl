<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="android" name="Android">
      <configuration>
        <option name="ALLOW_USER_CONFIGURATION" value="false" />
        <option name="GEN_FOLDER_RELATIVE_PATH_APT" value="/.android/gen" />
        <option name="GEN_FOLDER_RELATIVE_PATH_AIDL" value="/.android/gen" />
        <option name="MANIFEST_FILE_RELATIVE_PATH" value="/.android/AndroidManifest.xml" />
        <option name="RES_FOLDER_RELATIVE_PATH" value="/.android/res" />
        <option name="ASSETS_FOLDER_RELATIVE_PATH" value="/.android/assets" />
        <option name="LIBS_FOLDER_RELATIVE_PATH" value="/.android/libs" />
        <option name="PROGUARD_LOGS_FOLDER_RELATIVE_PATH" value="/.android/proguard_logs" />
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$/.android">
      <sourceFolder url="file://$MODULE_DIR$/.android/Flutter/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/.android/gen" isTestSource="false" generated="true" />
    </content>
    <orderEntry type="jdk" jdkName="Android API {{androidSdkVersion}} Platform" jdkType="Android SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Flutter for Android" level="project" />
  </component>
</module>
