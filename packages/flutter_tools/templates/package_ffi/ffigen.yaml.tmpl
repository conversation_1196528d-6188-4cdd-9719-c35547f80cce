# Run with `dart run ffigen --config ffigen.yaml`.
name: {{pluginDartClass}}Bindings
description: |
  Bindings for `src/{{projectName}}.h`.

  Regenerate bindings with `dart run ffigen --config ffigen.yaml`.
output: 'lib/{{projectName}}_bindings_generated.dart'
headers:
  entry-points:
    - 'src/{{projectName}}.h'
  include-directives:
    - 'src/{{projectName}}.h'
ffi-native:
preamble: |
  // ignore_for_file: always_specify_types
  // ignore_for_file: camel_case_types
  // ignore_for_file: non_constant_identifier_names
comments:
  style: any
  length: full
