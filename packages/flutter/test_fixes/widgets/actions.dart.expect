// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'package:flutter/widgets.dart';

void main() {
  // Changes made in https://github.com/flutter/flutter/pull/68921
  Actions.find(error: '');
  Actions.maybeFind(context);
  Actions.find(context);
  Actions.handler(context);
  Actions.handler(context);
  Actions.handler(error: '');
  Actions.invoke(error: '');
  Actions.maybeInvoke(context);
  Actions.invoke(context);
}
